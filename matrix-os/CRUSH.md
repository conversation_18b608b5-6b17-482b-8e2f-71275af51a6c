# Matrix OS - Development Guide

## Build/Test Commands
- `npm run dev` - Start development server with Turbo
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Code Style Guidelines

### Imports & Structure
- Use `'use client'` directive for client components
- Import React hooks first, then external libraries, then local imports
- Use `@/` path alias for imports (configured in tsconfig.json)
- Group imports: React → external → local components → types

### TypeScript & Types
- Strict TypeScript enabled - all types must be defined
- Use interface for object shapes, type for unions/primitives
- Define types in `/types/index.ts` for shared interfaces
- Use proper generic constraints and utility types

### Component Patterns
- Use default exports for components
- Props interfaces should be named `ComponentNameProps`
- Use `cn()` utility from `@/lib/utils` for conditional classes
- Prefer function components with hooks over class components

### Styling & Design
- Tailwind CSS with custom Matrix theme colors (`matrix-*`)
- Use semantic color tokens: `matrix-primary`, `matrix-accent`, `matrix-light`
- Consistent spacing with Tailwind scale (4, 8, 12, 16, etc.)
- Custom animations defined in tailwind.config.ts
- Glass morphism effects with backdrop-blur and matrix shadows

### State Management
- Use custom hooks for complex state (see `useWindowManager`)
- Prefer local state with useState/useReducer
- Use refs for DOM manipulation with proper typing

### Error Handling
- Use proper TypeScript error boundaries
- Handle async operations with try/catch
- Validate props and provide fallbacks