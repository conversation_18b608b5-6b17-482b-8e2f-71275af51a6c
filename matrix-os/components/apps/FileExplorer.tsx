'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Folder, 
  File, 
  Code, 
  FileText, 
  Image, 
  Database, 
  Settings,
  ArrowLeft,
  Home,
  Star,
  GitBranch,
  Cloud,
  Monitor,
  Brain,
  Shield
} from 'lucide-react'

interface FileExplorerProps {
  windowId: string
}

interface FileItem {
  id: string
  name: string
  type: 'folder' | 'file'
  icon?: any
  size?: string
  modified?: string
  description?: string
  content?: any
  parent?: string
}

const projectsData: FileItem[] = [
  {
    id: 'root',
    name: 'Projects',
    type: 'folder',
    icon: Folder,
  },
  {
    id: 'ai-projects',
    name: 'AI Projects',
    type: 'folder',
    icon: Brain,
    parent: 'root',
    description: 'AI and Machine Learning implementations'
  },
  {
    id: 'infrastructure',
    name: 'Infrastructure',
    type: 'folder',
    icon: Monitor,
    parent: 'root',
    description: 'Cloud and network infrastructure projects'
  },
  {
    id: 'security',
    name: 'Security',
    type: 'folder',
    icon: Shield,
    parent: 'root',
    description: 'Security and compliance projects'
  },
  {
    id: 'cloud-migration',
    name: 'Enterprise Cloud Migration',
    type: 'file',
    icon: Cloud,
    parent: 'infrastructure',
    size: '2.4 MB',
    modified: '2024-03-15',
    description: 'Strategic cloud migration for large enterprise',
    content: {
      title: 'Enterprise Cloud Migration Strategy',
      details: [
        '• Led migration of 200+ servers to Azure',
        '• Reduced infrastructure costs by 30%',
        '• Implemented disaster recovery procedures',
        '• Zero downtime migration strategy',
        '• Comprehensive security assessment'
      ],
      technologies: ['Azure', 'PowerShell', 'Terraform', 'Azure DevOps'],
      impact: 'Successfully migrated critical enterprise workloads with 30% cost reduction'
    }
  },
  {
    id: 'ai-anomaly-detection',
    name: 'AI Network Anomaly Detection',
    type: 'file',
    icon: Brain,
    parent: 'ai-projects',
    size: '1.8 MB',
    modified: '2024-02-20',
    description: 'ML-powered network security monitoring',
    content: {
      title: 'AI-Powered Network Anomaly Detection',
      details: [
        '• Developed ML model for real-time threat detection',
        '• 40% improvement in response times',
        '• Reduced false positives by 60%',
        '• Integration with existing SIEM systems',
        '• Automated threat response workflows'
      ],
      technologies: ['Python', 'TensorFlow', 'Scikit-learn', 'Azure ML', 'Splunk'],
      impact: 'Enhanced security posture with faster threat detection and response'
    }
  },
  {
    id: 'devops-pipeline',
    name: 'Automated CI/CD Pipeline',
    type: 'file',
    icon: GitBranch,
    parent: 'infrastructure',
    size: '1.2 MB',
    modified: '2024-01-10',
    description: 'DevOps automation and deployment pipeline',
    content: {
      title: 'Automated Deployment Pipeline',
      details: [
        '• Implemented CI/CD using Azure DevOps',
        '• 60% reduction in deployment time',
        '• Automated testing and quality gates',
        '• Infrastructure as Code (IaC)',
        '• Multi-environment deployment strategy'
      ],
      technologies: ['Azure DevOps', 'Docker', 'Kubernetes', 'Terraform', 'YAML'],
      impact: 'Streamlined development workflow with faster, more reliable deployments'
    }
  },
  {
    id: 'ai-customer-support',
    name: 'AI Customer Support System',
    type: 'file',
    icon: Brain,
    parent: 'ai-projects',
    size: '3.1 MB',
    modified: '2023-12-05',
    description: 'Intelligent customer support automation',
    content: {
      title: 'AI-Enhanced Customer Support System',
      details: [
        '• Integrated chatbot with existing support platform',
        '• 50% reduction in response times',
        '• Natural language processing for ticket routing',
        '• Sentiment analysis for priority handling',
        '• Knowledge base integration'
      ],
      technologies: ['Python', 'OpenAI API', 'NLP', 'React', 'Node.js'],
      impact: 'Improved customer satisfaction with faster, more accurate support'
    }
  },
  {
    id: 'security-audit',
    name: 'Comprehensive Security Audit',
    type: 'file',
    icon: Shield,
    parent: 'security',
    size: '4.7 MB',
    modified: '2023-11-15',
    description: 'Enterprise security assessment and remediation',
    content: {
      title: 'Enterprise Security Audit & Remediation',
      details: [
        '• Conducted full security assessment',
        '• Identified and remediated 150+ vulnerabilities',
        '• Implemented security monitoring tools',
        '• Staff security training program',
        '• Compliance framework implementation'
      ],
      technologies: ['Nessus', 'OpenVAS', 'Metasploit', 'Wireshark', 'PowerShell'],
      impact: 'Significantly improved security posture and compliance standing'
    }
  }
]

export default function FileExplorer({ windowId }: FileExplorerProps) {
  const [currentPath, setCurrentPath] = useState('root')
  const [selectedItem, setSelectedItem] = useState<FileItem | null>(null)
  const [viewMode, setViewMode] = useState<'list' | 'details'>('list')

  const getCurrentItems = () => {
    return projectsData.filter(item => item.parent === currentPath)
  }

  const getCurrentFolder = () => {
    return projectsData.find(item => item.id === currentPath)
  }

  const handleItemClick = (item: FileItem) => {
    if (item.type === 'folder') {
      setCurrentPath(item.id)
      setSelectedItem(null)
    } else {
      setSelectedItem(item)
    }
  }

  const handleBack = () => {
    const currentFolder = getCurrentFolder()
    if (currentFolder?.parent) {
      setCurrentPath(currentFolder.parent)
      setSelectedItem(null)
    }
  }

  const breadcrumbs = () => {
    const path = []
    let current = currentPath
    
    while (current && current !== 'root') {
      const folder = projectsData.find(item => item.id === current)
      if (folder) {
        path.unshift(folder)
        current = folder.parent || 'root'
      } else {
        break
      }
    }
    
    return [{ id: 'root', name: 'Projects' }, ...path]
  }

  return (
    <div className="h-full bg-matrix-surface text-matrix-light flex">
      {/* Sidebar */}
      <div className="w-64 bg-matrix-dark/50 border-r border-matrix-primary/20 p-4">
        <div className="space-y-4">
          {/* Navigation */}
          <div>
            <h3 className="text-sm font-semibold text-matrix-accent mb-2">Navigation</h3>
            <div className="space-y-1">
              <button
                onClick={() => { setCurrentPath('root'); setSelectedItem(null); }}
                className="w-full flex items-center px-3 py-2 text-sm rounded hover:bg-matrix-primary/10 transition-colors"
              >
                <Home className="w-4 h-4 mr-2" />
                Home
              </button>
              <button
                onClick={() => { setCurrentPath('ai-projects'); setSelectedItem(null); }}
                className="w-full flex items-center px-3 py-2 text-sm rounded hover:bg-matrix-primary/10 transition-colors"
              >
                <Brain className="w-4 h-4 mr-2" />
                AI Projects
              </button>
              <button
                onClick={() => { setCurrentPath('infrastructure'); setSelectedItem(null); }}
                className="w-full flex items-center px-3 py-2 text-sm rounded hover:bg-matrix-primary/10 transition-colors"
              >
                <Monitor className="w-4 h-4 mr-2" />
                Infrastructure
              </button>
              <button
                onClick={() => { setCurrentPath('security'); setSelectedItem(null); }}
                className="w-full flex items-center px-3 py-2 text-sm rounded hover:bg-matrix-primary/10 transition-colors"
              >
                <Shield className="w-4 h-4 mr-2" />
                Security
              </button>
            </div>
          </div>

          {/* Quick Stats */}
          <div>
            <h3 className="text-sm font-semibold text-matrix-accent mb-2">Portfolio Stats</h3>
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span className="text-matrix-light/70">Total Projects:</span>
                <span className="text-matrix-primary">5</span>
              </div>
              <div className="flex justify-between">
                <span className="text-matrix-light/70">AI Projects:</span>
                <span className="text-matrix-primary">2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-matrix-light/70">Infrastructure:</span>
                <span className="text-matrix-primary">2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-matrix-light/70">Security:</span>
                <span className="text-matrix-primary">1</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-matrix-primary/20 bg-matrix-dark/30">
          <div className="flex items-center gap-3 mb-3">
            {currentPath !== 'root' && (
              <button
                onClick={handleBack}
                className="p-1 rounded hover:bg-matrix-primary/20 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
              </button>
            )}
            <div className="flex items-center gap-2 text-sm">
              {breadcrumbs().map((crumb, index) => (
                <div key={crumb.id} className="flex items-center">
                  {index > 0 && <span className="mx-2 text-matrix-light/50">/</span>}
                  <span className="text-matrix-accent">{crumb.name}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-matrix-light/70">
              {getCurrentItems().length} items
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-1 text-xs rounded ${
                  viewMode === 'list' ? 'bg-matrix-primary/20 text-matrix-primary' : 'text-matrix-light/70'
                }`}
              >
                List
              </button>
              <button
                onClick={() => setViewMode('details')}
                className={`px-3 py-1 text-xs rounded ${
                  viewMode === 'details' ? 'bg-matrix-primary/20 text-matrix-primary' : 'text-matrix-light/70'
                }`}
              >
                Details
              </button>
            </div>
          </div>
        </div>

        {/* File List */}
        <div className="flex-1 flex">
          <div className="flex-1 p-4 overflow-y-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentPath}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="grid gap-2"
              >
                {getCurrentItems().map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    onClick={() => handleItemClick(item)}
                    className={`
                      p-3 rounded-lg cursor-pointer transition-all border
                      ${selectedItem?.id === item.id
                        ? 'bg-matrix-primary/20 border-matrix-primary'
                        : 'bg-matrix-dark/30 border-matrix-primary/10 hover:border-matrix-primary/30 hover:bg-matrix-primary/10'
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <item.icon className={`w-5 h-5 ${
                        item.type === 'folder' ? 'text-matrix-accent' : 'text-matrix-primary'
                      }`} />
                      <div className="flex-1">
                        <div className="font-medium text-matrix-light">{item.name}</div>
                        {item.description && (
                          <div className="text-xs text-matrix-light/60 mt-1">{item.description}</div>
                        )}
                        {viewMode === 'details' && item.type === 'file' && (
                          <div className="flex gap-4 text-xs text-matrix-light/50 mt-1">
                            <span>{item.size}</span>
                            <span>Modified: {item.modified}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* File Details Panel */}
          {selectedItem && selectedItem.content && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="w-80 border-l border-matrix-primary/20 bg-matrix-dark/30 p-4 overflow-y-auto"
            >
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-matrix-primary mb-2">{selectedItem.content.title}</h3>
                  <div className="text-sm text-matrix-light/70 space-y-1">
                    <div>Size: {selectedItem.size}</div>
                    <div>Modified: {selectedItem.modified}</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-semibold text-matrix-accent mb-2">Project Details</h4>
                  <div className="space-y-1">
                    {selectedItem.content.details.map((detail: string, index: number) => (
                      <div key={index} className="text-xs text-matrix-light">{detail}</div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-semibold text-matrix-accent mb-2">Technologies</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedItem.content.technologies.map((tech: string, index: number) => (
                      <span
                        key={index}
                        className="px-2 py-1 text-xs bg-matrix-primary/20 text-matrix-primary rounded"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-semibold text-matrix-accent mb-2">Impact</h4>
                  <p className="text-xs text-matrix-light">{selectedItem.content.impact}</p>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  )
}