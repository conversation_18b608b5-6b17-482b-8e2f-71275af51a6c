'use client'

import { ExternalLink, Globe, Github, Linkedin, Mail } from 'lucide-react'

interface PortfolioProps {
  windowId: string
}

export default function Portfolio({ windowId }: PortfolioProps) {
  return (
    <div className="h-full bg-gradient-to-br from-matrix-dark via-matrix-surface to-matrix-medium p-8 overflow-auto">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-r from-matrix-primary to-matrix-accent flex items-center justify-center">
            <Globe className="w-12 h-12 text-matrix-dark" />
          </div>
          <h1 className="text-4xl font-bold text-matrix-primary mb-4 font-display">
            Portfolio Website
          </h1>
          <p className="text-lg text-matrix-light/80 max-w-2xl mx-auto leading-relaxed">
            Explore my complete portfolio with detailed project showcases, professional experience, 
            and comprehensive information about my work in software development.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-matrix-surface/50 backdrop-blur-sm border border-matrix-primary/20 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-matrix-primary mb-3">Project Showcase</h3>
            <p className="text-matrix-light/70 mb-4">
              Detailed case studies of my latest projects with live demos, source code, and technical deep-dives.
            </p>
            <ul className="space-y-2 text-sm text-matrix-light/60">
              <li>• Interactive project galleries</li>
              <li>• Technical documentation</li>
              <li>• Live deployment links</li>
              <li>• Source code repositories</li>
            </ul>
          </div>

          <div className="bg-matrix-surface/50 backdrop-blur-sm border border-matrix-primary/20 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-matrix-primary mb-3">Professional Experience</h3>
            <p className="text-matrix-light/70 mb-4">
              Comprehensive overview of my career journey, skills, and professional achievements.
            </p>
            <ul className="space-y-2 text-sm text-matrix-light/60">
              <li>• Detailed work history</li>
              <li>• Skills and technologies</li>
              <li>• Certifications and awards</li>
              <li>• Professional references</li>
            </ul>
          </div>

          <div className="bg-matrix-surface/50 backdrop-blur-sm border border-matrix-primary/20 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-matrix-primary mb-3">Blog & Articles</h3>
            <p className="text-matrix-light/70 mb-4">
              Technical articles, tutorials, and insights from my development journey.
            </p>
            <ul className="space-y-2 text-sm text-matrix-light/60">
              <li>• Technical tutorials</li>
              <li>• Industry insights</li>
              <li>• Development tips</li>
              <li>• Project retrospectives</li>
            </ul>
          </div>

          <div className="bg-matrix-surface/50 backdrop-blur-sm border border-matrix-primary/20 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-matrix-primary mb-3">Contact & Connect</h3>
            <p className="text-matrix-light/70 mb-4">
              Multiple ways to get in touch and connect professionally.
            </p>
            <div className="flex space-x-4">
              <Github className="w-5 h-5 text-matrix-accent" />
              <Linkedin className="w-5 h-5 text-matrix-accent" />
              <Mail className="w-5 h-5 text-matrix-accent" />
              <Globe className="w-5 h-5 text-matrix-accent" />
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-matrix-primary/10 to-matrix-accent/10 border border-matrix-primary/30 rounded-xl p-8">
          <h2 className="text-2xl font-bold text-matrix-primary mb-4">Ready to Explore?</h2>
          <p className="text-matrix-light/80 mb-6 max-w-xl mx-auto">
            Visit my full portfolio website to see everything in detail and get in touch for potential collaborations.
          </p>
          <a
            href="../"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-matrix-primary to-matrix-accent text-matrix-dark font-semibold rounded-lg hover:shadow-matrix-lg hover:scale-105 transition-all duration-300 group"
          >
            <span>Visit Portfolio Site</span>
            <ExternalLink className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
          </a>
        </div>
      </div>
    </div>
  )
}