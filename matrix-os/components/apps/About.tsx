'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  User, 
  Briefcase, 
  GraduationCap, 
  Award, 
  Code, 
  Mail,
  Phone,
  Globe,
  Github,
  Linkedin,
  MapPin,
  Calendar
} from 'lucide-react'

interface AboutProps {
  windowId: string
}

const tabs = [
  { id: 'about', label: 'About', icon: User },
  { id: 'experience', label: 'Experience', icon: Briefcase },
  { id: 'education', label: 'Education', icon: GraduationCap },
  { id: 'certifications', label: 'Certifications', icon: Award },
  { id: 'skills', label: 'Skills', icon: Code },
  { id: 'contact', label: 'Contact', icon: Mail },
]

const skillsData = [
  { name: 'Python Programming', level: 95, category: 'Programming' },
  { name: 'AI Integrations (API & Local)', level: 90, category: 'AI/ML' },
  { name: 'Network Engineering', level: 95, category: 'Infrastructure' },
  { name: 'Azure, AWS, GCP IaaS Hosting', level: 95, category: 'Cloud' },
  { name: 'Linux Administration', level: 90, category: 'Systems' },
  { name: 'SQL Server Management', level: 85, category: 'Database' },
  { name: 'Creative Problem Solving', level: 95, category: 'Soft Skills' },
  { name: 'VPN Configuration (P2S/S2S)', level: 90, category: 'Network' },
]

const experienceData = [
  {
    title: 'Senior Technical Consultant of Hosting Operations',
    company: 'Dye & Durham Corporation (formerly Korbicom)',
    duration: 'Aug 2022 - Present',
    responsibilities: [
      'Onboard and deploy new clients into Azure hosting environment',
      'Maintain Azure hosting environment and deployed resources',
      'Troubleshoot deployment issues and manage SQL server environments',
      'Compile Site Assessment Reports',
      'Provide comprehensive support for GhostPractice-related technical issues',
      'Offer expert guidance on technology integration',
      'Led beta phase onboarding of clients for cloud offerings expansion'
    ]
  },
  {
    title: 'Infrastructure Engineer',
    company: 'Ecentric Payment Systems – Cape Town',
    duration: 'Jul 2016 – Jul 2022',
    responsibilities: [
      'Configured and maintained network devices',
      'Implemented infrastructure changes and upgrades',
      'Monitored infrastructure and addressed security vulnerabilities',
      'Performed disaster recovery operations and regular data backups',
      'Provided cost estimates for project budgets',
      'Utilized Linux and analytical skills for troubleshooting'
    ]
  }
]

const certifications = [
  'Cisco Certified Entry Networking Technician (CCENT)',
  'Cisco Certified Network Associate Course (NMU)',
  'Cisco Certified Network Professional Course (NMU)',
  'AZ-900: Microsoft Certified: Azure Fundamentals',
  'Artificial Intelligence Fundamentals (IBM)',
  'LPIC-1 Linux Administrator Course (Torque IT)',
  'NSE 1 and NSE 2 - Fortinet Network Security Associate'
]

const education = [
  {
    degree: 'Research in Informatics in Practice',
    institution: 'University of South Africa (UNISA)',
    year: '2015'
  },
  {
    degree: 'Bachelor of Technology in Information Technology: Communication Networks',
    institution: 'Nelson Mandela University',
    year: '2014'
  },
  {
    degree: 'National Diploma in Information Technology: Support Services',
    institution: 'Nelson Mandela University',
    year: '2010 - 2014'
  }
]

export default function About({ windowId }: AboutProps) {
  const [activeTab, setActiveTab] = useState('about')

  const renderSkillBar = (skill: any, index: number) => (
    <motion.div
      key={skill.name}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.1 }}
      className="mb-4"
    >
      <div className="flex justify-between mb-1">
        <span className="text-sm font-medium text-matrix-light">{skill.name}</span>
        <span className="text-sm text-matrix-accent">{skill.level}%</span>
      </div>
      <div className="w-full bg-matrix-surface/50 rounded-full h-2">
        <motion.div
          className="bg-gradient-to-r from-matrix-primary to-matrix-accent h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${skill.level}%` }}
          transition={{ duration: 1, delay: index * 0.1 }}
        />
      </div>
      <div className="text-xs text-matrix-light/60 mt-1">{skill.category}</div>
    </motion.div>
  )

  const renderContent = () => {
    switch (activeTab) {
      case 'about':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-32 h-32 bg-gradient-to-br from-matrix-primary to-matrix-accent rounded-full mx-auto mb-4 flex items-center justify-center">
                <User className="w-16 h-16 text-black" />
              </div>
              <h1 className="text-2xl font-bold text-matrix-primary mb-2">Aubrey Zemba</h1>
              <p className="text-matrix-accent text-lg mb-4">
                Senior Cloud Consultant @ D&D | CEO @ AZLabs | AI Solutions Expert
              </p>
            </div>
            
            <div className="bg-matrix-surface/30 rounded-lg p-6 border border-matrix-primary/20">
              <h3 className="text-lg font-semibold text-matrix-primary mb-3">About Me</h3>
              <p className="text-matrix-light leading-relaxed mb-4">
                As a Senior Technical Consultant specializing in Hosting Operations, I bring a wealth of experience in cloud technology, network security, and AI integration. My expertise spans across various IT domains, allowing me to design, implement, and manage robust IT infrastructures and hosting operations.
              </p>
              <p className="text-matrix-light leading-relaxed">
                With a strong foundation in computer security and data privacy, I excel in configuring network devices and maintaining SQL environments. My recent focus has been on leveraging AI to enhance existing systems, driving efficiency and automation in IT operations.
              </p>
            </div>
          </motion.div>
        )

      case 'experience':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-xl font-bold text-matrix-primary mb-4">Work Experience</h2>
            {experienceData.map((job, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.2 }}
                className="bg-matrix-surface/30 rounded-lg p-6 border border-matrix-primary/20 border-l-4 border-l-matrix-primary"
              >
                <h3 className="text-lg font-semibold text-matrix-accent mb-1">{job.title}</h3>
                <p className="text-matrix-light font-medium mb-2">{job.company}</p>
                <div className="flex items-center text-matrix-primary text-sm mb-4">
                  <Calendar className="w-4 h-4 mr-2" />
                  {job.duration}
                </div>
                <ul className="space-y-2">
                  {job.responsibilities.map((resp, respIndex) => (
                    <li key={respIndex} className="text-matrix-light text-sm flex items-start">
                      <div className="w-2 h-2 bg-matrix-primary rounded-full mt-2 mr-3 flex-shrink-0" />
                      {resp}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </motion.div>
        )

      case 'education':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-xl font-bold text-matrix-primary mb-4">Education</h2>
            {education.map((edu, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.2 }}
                className="bg-matrix-surface/30 rounded-lg p-6 border border-matrix-primary/20"
              >
                <h3 className="text-lg font-semibold text-matrix-accent mb-2">{edu.degree}</h3>
                <p className="text-matrix-light font-medium mb-2">{edu.institution}</p>
                <div className="flex items-center text-matrix-primary text-sm">
                  <Calendar className="w-4 h-4 mr-2" />
                  {edu.year}
                </div>
              </motion.div>
            ))}
          </motion.div>
        )

      case 'certifications':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-xl font-bold text-matrix-primary mb-4">Certifications</h2>
            <div className="grid gap-4">
              {certifications.map((cert, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-matrix-surface/30 rounded-lg p-4 border border-matrix-primary/20 hover:border-matrix-primary/40 transition-colors"
                >
                  <div className="flex items-center">
                    <Award className="w-5 h-5 text-matrix-primary mr-3" />
                    <span className="text-matrix-light">{cert}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )

      case 'skills':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-xl font-bold text-matrix-primary mb-4">Technical Skills</h2>
            <div className="grid gap-4">
              {skillsData.map((skill, index) => renderSkillBar(skill, index))}
            </div>
          </motion.div>
        )

      case 'contact':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-xl font-bold text-matrix-primary mb-4">Contact Information</h2>
            <div className="grid gap-4">
              {[
                { icon: Mail, label: 'Email', value: '<EMAIL>', href: 'mailto:<EMAIL>' },
                { icon: Globe, label: 'Website', value: 'aubreyzemba.com', href: 'https://aubreyzemba.com' },
                { icon: Linkedin, label: 'LinkedIn', value: 'linkedin.com/in/aubrey-zemba', href: 'https://linkedin.com/in/aubrey-zemba' },
                { icon: Github, label: 'GitHub', value: 'github.com/TH33ORACL3', href: 'https://github.com/TH33ORACL3' },
                { icon: Phone, label: 'Phone', value: '+27 81 252 7098', href: 'tel:+27812527098' },
                { icon: MapPin, label: 'Location', value: 'Cape Town, South Africa', href: null },
              ].map((contact, index) => (
                <motion.div
                  key={contact.label}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-matrix-surface/30 rounded-lg p-4 border border-matrix-primary/20"
                >
                  <div className="flex items-center">
                    <contact.icon className="w-5 h-5 text-matrix-primary mr-3" />
                    <div>
                      <div className="text-sm text-matrix-light/60">{contact.label}</div>
                      {contact.href ? (
                        <a
                          href={contact.href}
                          target={contact.href.startsWith('http') ? '_blank' : undefined}
                          rel={contact.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                          className="text-matrix-accent hover:text-matrix-primary transition-colors"
                        >
                          {contact.value}
                        </a>
                      ) : (
                        <span className="text-matrix-light">{contact.value}</span>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )

      default:
        return null
    }
  }

  return (
    <div className="h-full bg-gradient-to-br from-matrix-dark/95 to-black/90 text-matrix-light flex">
      {/* Sidebar */}
      <div className="w-48 bg-matrix-surface/50 border-r border-matrix-primary/20 p-4">
        <nav className="space-y-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors
                ${activeTab === tab.id
                  ? 'bg-matrix-primary/20 text-matrix-primary border border-matrix-primary/30'
                  : 'text-matrix-light hover:bg-matrix-primary/10 hover:text-matrix-accent'
                }
              `}
            >
              <tab.icon className="w-4 h-4 mr-3" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 overflow-y-auto scrollbar-thin">
        {renderContent()}
      </div>
    </div>
  )
}