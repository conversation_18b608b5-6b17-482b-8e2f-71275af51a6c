'use client'

import { useState } from 'react'
import { 
  Monitor, 
  Palette, 
  Volume2, 
  Wifi, 
  Shield, 
  User, 
  Bell, 
  Keyboard,
  Mouse,
  HardDrive,
  Battery,
  Bluetooth,
  Settings as SettingsIcon
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SettingsProps {
  windowId: string
}

const settingsCategories = [
  { id: 'display', name: 'Display', icon: Monitor },
  { id: 'appearance', name: 'Appearance', icon: Palette },
  { id: 'audio', name: 'Audio', icon: Volume2 },
  { id: 'network', name: 'Network', icon: Wifi },
  { id: 'security', name: 'Security', icon: Shield },
  { id: 'accounts', name: 'Accounts', icon: User },
  { id: 'notifications', name: 'Notifications', icon: Bell },
  { id: 'keyboard', name: 'Keyboard', icon: Keyboard },
  { id: 'mouse', name: 'Mouse', icon: Mouse },
  { id: 'storage', name: 'Storage', icon: HardDrive },
  { id: 'power', name: 'Power', icon: Battery },
  { id: 'bluetooth', name: 'Bluetooth', icon: Bluetooth },
]

export default function Settings({ windowId }: SettingsProps) {
  const [activeCategory, setActiveCategory] = useState('display')

  const renderDisplaySettings = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-matrix-primary mb-6">Display Settings</h2>
      
      <div className="space-y-4">
        <div className="bg-matrix-surface/50 border border-matrix-primary/20 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-matrix-light mb-3">Resolution</h3>
          <select className="w-full bg-matrix-dark border border-matrix-primary/30 rounded px-3 py-2 text-matrix-light">
            <option>1920 x 1080 (Recommended)</option>
            <option>2560 x 1440</option>
            <option>3840 x 2160</option>
          </select>
        </div>

        <div className="bg-matrix-surface/50 border border-matrix-primary/20 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-matrix-light mb-3">Scale</h3>
          <div className="flex space-x-4">
            {[100, 125, 150, 175, 200].map(scale => (
              <button
                key={scale}
                className={cn(
                  "px-4 py-2 rounded border transition-colors",
                  scale === 100 
                    ? "bg-matrix-primary text-matrix-dark border-matrix-primary" 
                    : "bg-matrix-dark border-matrix-primary/30 text-matrix-light hover:border-matrix-primary/50"
                )}
              >
                {scale}%
              </button>
            ))}
          </div>
        </div>

        <div className="bg-matrix-surface/50 border border-matrix-primary/20 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-matrix-light mb-3">Matrix Rain Intensity</h3>
          <input 
            type="range" 
            min="0" 
            max="100" 
            defaultValue="40"
            className="w-full accent-matrix-primary"
          />
          <div className="flex justify-between text-sm text-matrix-light/60 mt-1">
            <span>Off</span>
            <span>Maximum</span>
          </div>
        </div>
      </div>
    </div>
  )

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-matrix-primary mb-6">Appearance</h2>
      
      <div className="space-y-4">
        <div className="bg-matrix-surface/50 border border-matrix-primary/20 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-matrix-light mb-3">Theme</h3>
          <div className="grid grid-cols-3 gap-3">
            {['Matrix Classic', 'Matrix Blue', 'Matrix Red'].map(theme => (
              <button
                key={theme}
                className={cn(
                  "p-4 rounded-lg border transition-colors text-center",
                  theme === 'Matrix Classic'
                    ? "bg-matrix-primary/20 border-matrix-primary text-matrix-primary"
                    : "bg-matrix-dark border-matrix-primary/30 text-matrix-light hover:border-matrix-primary/50"
                )}
              >
                <div className="w-8 h-8 rounded-full mx-auto mb-2 bg-gradient-to-r from-matrix-primary to-matrix-accent"></div>
                <span className="text-sm">{theme}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="bg-matrix-surface/50 border border-matrix-primary/20 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-matrix-light mb-3">Window Effects</h3>
          <div className="space-y-3">
            <label className="flex items-center space-x-3">
              <input type="checkbox" defaultChecked className="accent-matrix-primary" />
              <span className="text-matrix-light">Glass morphism effects</span>
            </label>
            <label className="flex items-center space-x-3">
              <input type="checkbox" defaultChecked className="accent-matrix-primary" />
              <span className="text-matrix-light">Window shadows</span>
            </label>
            <label className="flex items-center space-x-3">
              <input type="checkbox" defaultChecked className="accent-matrix-primary" />
              <span className="text-matrix-light">Smooth animations</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  )

  const renderCategoryContent = () => {
    switch (activeCategory) {
      case 'display':
        return renderDisplaySettings()
      case 'appearance':
        return renderAppearanceSettings()
      default:
        return (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <SettingsIcon className="w-16 h-16 text-matrix-primary/50 mx-auto mb-4" />
              <p className="text-matrix-light/60">Settings for {settingsCategories.find(c => c.id === activeCategory)?.name} coming soon...</p>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="h-full bg-gradient-to-br from-matrix-dark via-matrix-surface to-matrix-medium flex">
      {/* Sidebar */}
      <div className="w-64 bg-matrix-surface/30 border-r border-matrix-primary/20 p-4">
        <h1 className="text-xl font-bold text-matrix-primary mb-6 flex items-center">
          <SettingsIcon className="w-6 h-6 mr-2" />
          System Settings
        </h1>
        
        <nav className="space-y-1">
          {settingsCategories.map(category => {
            const Icon = category.icon
            return (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={cn(
                  "w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors text-left",
                  activeCategory === category.id
                    ? "bg-matrix-primary/20 text-matrix-primary border border-matrix-primary/30"
                    : "text-matrix-light/70 hover:bg-matrix-primary/10 hover:text-matrix-light"
                )}
              >
                <Icon className="w-5 h-5" />
                <span>{category.name}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-auto">
        {renderCategoryContent()}
      </div>
    </div>
  )
}