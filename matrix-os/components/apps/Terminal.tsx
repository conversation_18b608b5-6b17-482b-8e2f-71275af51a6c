'use client'

import { useState, useEffect, useRef } from 'react'
import { formatTime } from '@/lib/utils'

interface TerminalProps {
  windowId: string
}

interface TerminalLine {
  type: 'input' | 'output' | 'error'
  content: string
  timestamp: Date
}

const WELCOME_MESSAGE = `
 ╔═══════════════════════════════════════════════════════════════╗
 ║                        MATRIX TERMINAL                        ║
 ║                     Welcome to the Matrix                     ║
 ║                                                               ║
 ║  "There is no spoon" - The Matrix                            ║
 ║                                                               ║
 ║  Type 'help' for available commands                          ║
 ║  Type 'neo' to see who you really are                        ║
 ╚═══════════════════════════════════════════════════════════════╝
`

const COMMANDS = {
  help: {
    description: 'Show available commands',
    output: `Available commands:
  help     - Show this help message
  clear    - Clear the terminal
  neo      - Reveal your true identity
  whoami   - Display current user
  date     - Show current date and time
  ls       - List directory contents
  pwd      - Print working directory
  skills   - Display <PERSON>'s technical skills
  projects - Show featured projects
  contact  - Display contact information
  matrix   - Enter the Matrix
  hack     - Execute hack sequence
  system   - Show system information
  uptime   - Display system uptime
  ps       - List running processes
  top      - Show system performance
  cat      - Display file contents
  history  - Show command history
  exit     - Close terminal`
  },
  clear: {
    description: 'Clear terminal output',
    output: null
  },
  neo: {
    description: 'Reveal your true identity',
    output: `
  ██╗   ██╗ ██████╗ ██╗   ██╗     █████╗ ██████╗ ███████╗
  ╚██╗ ██╔╝██╔═══██╗██║   ██║    ██╔══██╗██╔══██╗██╔════╝
   ╚████╔╝ ██║   ██║██║   ██║    ███████║██████╔╝█████╗  
    ╚██╔╝  ██║   ██║██║   ██║    ██╔══██║██╔══██╗██╔══╝  
     ██║   ╚██████╔╝╚██████╔╝    ██║  ██║██║  ██║███████╗
     ╚═╝    ╚═════╝  ╚═════╝     ╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝
     
  ████████╗██╗  ██╗███████╗     ██████╗ ███╗   ██╗███████╗
  ╚══██╔══╝██║  ██║██╔════╝    ██╔═══██╗████╗  ██║██╔════╝
     ██║   ███████║█████╗      ██║   ██║██╔██╗ ██║█████╗  
     ██║   ██╔══██║██╔══╝      ██║   ██║██║╚██╗██║██╔══╝  
     ██║   ██║  ██║███████╗    ╚██████╔╝██║ ╚████║███████╗
     ╚═╝   ╚═╝  ╚═╝╚══════╝     ╚═════╝ ╚═╝  ╚═══╝╚══════╝

  "The Matrix has you..." - You are THE ONE.`
  },
  whoami: {
    description: 'Display current user',
    output: 'aubrey@matrix-os:~$ Senior Cloud Consultant & AI Solutions Expert'
  },
  date: {
    description: 'Show current date and time',
    output: () => new Date().toString()
  },
  ls: {
    description: 'List directory contents',
    output: `total 8
drwxr-xr-x  2 <USER> <GROUP> 4096 Jun 30 2024 Projects/
drwxr-xr-x  2 <USER> <GROUP> 4096 Jun 30 2024 Skills/
drwxr-xr-x  2 <USER> <GROUP> 4096 Jun 30 2024 Experience/
drwxr-xr-x  2 <USER> <GROUP> 4096 Jun 30 2024 Certifications/
-rw-r--r--  1 <USER> <GROUP>  256 Jun 30 2024 about.txt
-rw-r--r--  1 <USER> <GROUP>  128 Jun 30 2024 contact.txt
-rwxr-xr-x  1 <USER> <GROUP> 1024 Jun 30 2024 matrix.sh`
  },
  pwd: {
    description: 'Print working directory',
    output: '/home/<USER>/matrix-os'
  },
  skills: {
    description: 'Display technical skills',
    output: `Technical Skills:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• Python Programming                          ████████████████████ 95%
• AI Integrations (API & Local)              ████████████████████ 90%
• Network Engineering                        ████████████████████ 95%
• Azure, AWS, GCP IaaS Hosting               ████████████████████ 95%  
• Linux Administration                       ████████████████████ 90%
• SQL Server Management                      ████████████████████ 85%
• Creative Problem Solving                   ████████████████████ 95%
• VPN Configuration (P2S/S2S)               ████████████████████ 90%
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`
  },
  projects: {
    description: 'Show featured projects',
    output: `Featured Projects:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[1] AI-Powered Network Anomaly Detection
    └─ ML model for network security with 40% faster response times
    
[2] Enterprise Cloud Migration Strategy  
    └─ Led Azure migration resulting in 30% cost savings
    
[3] Automated Deployment Pipeline
    └─ CI/CD pipeline reducing deployment time by 60%
    
[4] AI-Enhanced Customer Support System
    └─ Integrated chatbot improving satisfaction by 50%
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`
  },
  contact: {
    description: 'Display contact information',
    output: `Contact Information:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📧 Email:    <EMAIL>
🌐 Website:  https://aubreyzemba.com
💼 LinkedIn: https://linkedin.com/in/aubrey-zemba
👨‍💻 GitHub:   https://github.com/TH33ORACL3
📱 Phone:    +27 81 252 7098
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`
  },
  matrix: {
    description: 'Enter the Matrix',
    output: `
  ┌─ ENTERING THE MATRIX ─────────────────────────────────────────┐
  │                                                               │
  │  "Unfortunately, no one can be told what the Matrix is.      │
  │   You have to see it for yourself."                          │
  │                                                               │
  │  Wake up, Neo... The Matrix has you...                       │
  │  Follow the white rabbit...                                   │
  │                                                               │
  │  [MATRIX PROTOCOL INITIALIZED]                                │
  │  [JACKING IN...]                                              │
  │  [CONNECTION ESTABLISHED]                                     │
  │                                                               │
  └───────────────────────────────────────────────────────────────┘`
  },
  hack: {
    description: 'Execute hack sequence',
    output: `
  ╔═══════════════════════════════════════════════════════════════╗
  ║                        HACK SEQUENCE                          ║
  ╠═══════════════════════════════════════════════════════════════╣
  ║  [INITIATING HACK PROTOCOL...]                                ║
  ║  [BYPASSING FIREWALL...]                            ████ 25%  ║
  ║  [DECRYPTING ENCRYPTED DATA...]                     ████ 50%  ║
  ║  [ACCESSING MAINFRAME...]                           ████ 75%  ║
  ║  [UPLOADING VIRUS...]                               ████ 100% ║
  ║                                                               ║
  ║  ACCESS GRANTED: Welcome to AZ Labs Security Matrix          ║
  ║  "I'm not a hacker, I'm a security consultant" - Aubrey      ║
  ╚═══════════════════════════════════════════════════════════════╝`
  },
  system: {
    description: 'Show system information',
    output: `System Information:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
OS: Matrix OS v2.0.24 (Zion Edition)
Kernel: Matrix-5.15.0-core
Architecture: x86_64
CPU: Neo Quantum Processor 8-core @ 3.2GHz
Memory: 32GB DDR5 (16GB available)
Storage: 2TB SSD (Encrypted with AES-256)
Graphics: Agent Smith Suppression Unit
Network: Red Pill Ethernet @ 1Gbps
Security: Morpheus Protocol Active
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`
  },
  uptime: {
    description: 'Display system uptime',
    output: () => {
      const uptime = Math.floor(Math.random() * 100) + 1;
      return `Matrix OS uptime: ${uptime} days, 23:42:15
Load average: 0.42, 0.69, 1.33
Users logged in: 1 (The One)
Processes: 267 total, 12 running, 255 sleeping
Agent processes detected: 0 (All clear)`;
    }
  },
  ps: {
    description: 'List running processes',
    output: `PID    USER     %CPU  %MEM  COMMAND
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
1      root     0.1   0.8   /sbin/init
42     matrix   2.1   4.2   matrix-os-desktop
69     aubrey   1.8   8.4   ai-assistant-daemon
123    neo      0.0   0.1   red-pill-monitor
256    system   3.2   12.1  cloud-sync-service
314    root     0.5   2.1   security-monitor
512    aubrey   15.2  24.8  neural-network-engine
666    deleted  0.0   0.0   [agent-scanner]
1024   matrix   5.8   16.4  reality-distortion-field
2048   aubrey   8.1   32.1  consciousness-backup
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`
  },
  top: {
    description: 'Show system performance',
    output: () => {
      const cpu = Math.floor(Math.random() * 30) + 5;
      const memory = Math.floor(Math.random() * 40) + 30;
      return `Matrix OS Performance Monitor
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
CPU Usage:    ${cpu}%  [████████████████████████████████████████]
Memory:       ${memory}%  [████████████████████████████████████████]
Swap:         2%   [██████████████████████████████████████████]
Network I/O:  142 KB/s ↑  2.4 MB/s ↓
Disk I/O:     8.2 MB/s read, 4.1 MB/s write
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Top Processes:
neural-network-engine    24.8% CPU, 8.2 GB RAM
consciousness-backup     16.4% CPU, 4.1 GB RAM
reality-distortion       12.1% CPU, 2.8 GB RAM`;
    }
  },
  cat: {
    description: 'Display file contents',
    output: (args?: string) => {
      const files: Record<string, string> = {
        'about.txt': `About Aubrey Zemba
═══════════════════
Senior Technical Consultant specializing in Hosting Operations
at Dye & Durham Corporation. Expert in cloud technology, 
network security, and AI integration.

"Bridging the gap between technology and possibility."`,
        'contact.txt': `Contact Information
═══════════════════
Email: <EMAIL>
Website: https://aubreyzemba.com
LinkedIn: https://linkedin.com/in/aubrey-zemba
GitHub: https://github.com/TH33ORACL3
Phone: +27 81 252 7098`,
        'matrix.sh': `#!/bin/bash
# Matrix OS Boot Script
echo "Initializing Matrix Protocol..."
echo "Loading consciousness backup..."
echo "Establishing connection to Zion..."
echo "Welcome to the real world."`,
      };
      
      if (!args || args.trim() === '') {
        return 'cat: missing file operand\nTry: cat about.txt, cat contact.txt, or cat matrix.sh';
      }
      
      const filename = args.trim();
      return files[filename] || `cat: ${filename}: No such file or directory`;
    }
  },
  history: {
    description: 'Show command history',
    output: () => {
      return `Command History:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
1   whoami
2   ls
3   skills
4   projects
5   matrix
6   hack
7   system
8   ps
9   top
10  history`;
    }
  },
}

export default function Terminal({ windowId }: TerminalProps) {
  const [lines, setLines] = useState<TerminalLine[]>([])
  const [currentInput, setCurrentInput] = useState('')
  const [commandHistory, setCommandHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  
  const terminalRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    // Show welcome message on mount
    setLines([{
      type: 'output',
      content: WELCOME_MESSAGE,
      timestamp: new Date()
    }])
  }, [])

  useEffect(() => {
    // Auto-scroll to bottom
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [lines])

  useEffect(() => {
    // Focus input when component mounts
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const executeCommand = (command: string) => {
    const parts = command.trim().split(/\s+/)
    const cmd = parts[0].toLowerCase()
    const args = parts.slice(1).join(' ')
    
    // Add input to history
    setLines(prev => [...prev, {
      type: 'input',
      content: `aubrey@matrix-os:~$ ${command}`,
      timestamp: new Date()
    }])

    // Add to command history
    if (command.trim()) {
      setCommandHistory(prev => [...prev, command])
    }

    // Execute command
    if (cmd === 'clear') {
      setLines([])
      return
    }

    if (cmd === 'exit') {
      setLines(prev => [...prev, {
        type: 'output',
        content: 'Connection to Matrix terminated. Goodbye.',
        timestamp: new Date()
      }])
      return
    }

    const commandInfo = COMMANDS[cmd as keyof typeof COMMANDS]
    if (commandInfo) {
      let output = commandInfo.output
      if (typeof output === 'function') {
        output = output(args)
      }
      
      if (output) {
        setLines(prev => [...prev, {
          type: 'output',
          content: output,
          timestamp: new Date()
        }])
      }
    } else {
      setLines(prev => [...prev, {
        type: 'error',
        content: `Command not found: ${cmd}. Type 'help' for available commands.`,
        timestamp: new Date()
      }])
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (currentInput.trim()) {
      executeCommand(currentInput)
    }
    setCurrentInput('')
    setHistoryIndex(-1)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault()
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1)
        setHistoryIndex(newIndex)
        setCurrentInput(commandHistory[newIndex])
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1)
          setCurrentInput('')
        } else {
          setHistoryIndex(newIndex)
          setCurrentInput(commandHistory[newIndex])
        }
      }
    }
  }

  return (
    <div className="h-full flex flex-col bg-black text-matrix-light font-mono">
      {/* Terminal Content */}
      <div 
        ref={terminalRef}
        className="flex-1 p-4 overflow-y-auto scrollbar-thin"
        style={{ fontFamily: 'JetBrains Mono, Consolas, Monaco, "Lucida Console", monospace' }}
      >
        {lines.map((line, index) => (
          <div key={index} className="flex items-start gap-2 min-h-[1.5rem] mb-1 group">
            <span className="text-matrix-primary/60 text-xs flex-shrink-0 group-hover:text-matrix-primary/80 transition-colors">
              [{formatTime(line.timestamp)}]
            </span>
            <div className="flex-1 min-w-0">
              <span 
                className={`
                  leading-relaxed
                  ${line.type === 'error' ? 'text-red-400' : ''}
                  ${line.type === 'output' ? 'text-matrix-light' : ''}
                  ${line.type === 'input' ? 'text-matrix-accent font-medium' : ''}
                `}
              >
                <pre className="whitespace-pre-wrap font-mono text-sm">{line.content}</pre>
              </span>
            </div>
          </div>
        ))}
      </div>
      
      {/* Input Form */}
      <div className="border-t border-matrix-primary/30 p-4 bg-gradient-to-r from-matrix-surface/20 to-matrix-surface/10 backdrop-blur-md">
        <form onSubmit={handleSubmit} className="w-full">
          <div className="flex items-center gap-2 bg-matrix-primary/10 rounded-lg px-3 py-2 border border-matrix-primary/20 hover:border-matrix-primary/40 transition-colors focus-within:border-matrix-primary/60 focus-within:ring-1 focus-within:ring-matrix-primary/20">
            <span className="text-matrix-primary/60 text-xs flex-shrink-0">
              [{formatTime(new Date())}]
            </span>
            <span className="text-matrix-accent font-semibold flex-shrink-0">aubrey@matrix-os:~$</span>
            <input
              ref={inputRef}
              type="text"
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyDown={handleKeyDown}
              className="flex-1 bg-transparent border-none outline-none text-matrix-primary caret-matrix-primary font-medium placeholder-matrix-light/40 focus:placeholder-matrix-light/60 text-sm"
              placeholder="Enter command..."
              autoComplete="off"
              spellCheck="false"
            />
            <div className="w-0.5 h-4 bg-matrix-primary animate-pulse flex-shrink-0" />
          </div>
        </form>
      </div>
    </div>
  )
}