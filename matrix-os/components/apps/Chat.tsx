'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Send, Bot, User, Sparkles } from 'lucide-react'

interface Message {
  id: string
  content: string
  sender: 'user' | 'ai'
  timestamp: Date
}

interface ChatProps {
  windowId: string
}

export default function Chat({ windowId }: ChatProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm <PERSON>'s AI assistant. I can help you learn more about his experience, skills, and projects. What would you like to know?",
      sender: 'ai',
      timestamp: new Date()
    }
  ])
  const [input, setInput] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const predefinedResponses: Record<string, string> = {
    'experience': "<PERSON> has over 8 years of experience in IT infrastructure, currently working as a Senior Technical Consultant at Dye & Durham Corporation. He specializes in Azure hosting operations, client onboarding, and cloud infrastructure management.",
    'skills': "His key skills include Python programming, AI integrations, network engineering, Linux administration, Azure/AWS/GCP hosting, SQL Server management, and SSL certificate administration. He's also certified in various technologies including Azure Fundamentals and Cisco networking.",
    'projects': "Some notable projects include AI-powered network anomaly detection, cloud migration strategies for enterprises, automated deployment pipelines, and AI-enhanced customer support systems. He's passionate about integrating AI into existing IT operations.",
    'education': "Aubrey holds a Bachelor of Technology in Information Technology: Communication Networks from Nelson Mandela University (2014) and has completed Research in Informatics in Practice at UNISA (2015).",
    'contact': "You can reach <NAME_EMAIL>, visit his website at AubreyZemba.com, or connect on LinkedIn. He's also active on GitHub as TH33ORACL3.",
    'ai': "Aubrey specializes in AI integrations both through APIs and local implementations. He's experienced with prompt engineering, NLP, and leveraging AI to enhance IT operations and customer support systems.",
    'azure': "Aubrey is an Azure expert with hands-on experience in hosting operations, client onboarding, resource management, and troubleshooting. He holds the AZ-900 Azure Fundamentals certification.",
    'default': "That's an interesting question! While I don't have specific information about that topic, I'd recommend reaching out to Aubrey <NAME_EMAIL> for more detailed discussions about his work and expertise."
  }

  const generateResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase()
    
    for (const [key, response] of Object.entries(predefinedResponses)) {
      if (key !== 'default' && lowerMessage.includes(key)) {
        return response
      }
    }
    
    return predefinedResponses.default
  }

  const handleSend = async () => {
    if (!input.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input.trim(),
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsTyping(true)

    // Simulate AI response delay
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: generateResponse(userMessage.content),
        sender: 'ai',
        timestamp: new Date()
      }
      
      setMessages(prev => [...prev, aiResponse])
      setIsTyping(false)
    }, 1000 + Math.random() * 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const quickQuestions = [
    "Tell me about your experience",
    "What are your main skills?",
    "Show me your projects",
    "How can I contact you?",
    "What AI work do you do?"
  ]

  return (
    <div className="h-full flex flex-col bg-matrix-surface text-matrix-light font-mono">
      {/* Header */}
      <div className="p-4 border-b border-matrix-primary/20 bg-gradient-to-r from-matrix-surface to-matrix-medium">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-matrix-primary to-matrix-accent flex items-center justify-center">
            <Bot size={16} />
          </div>
          <div>
            <h3 className="font-semibold text-matrix-accent">AI Assistant</h3>
            <p className="text-xs text-matrix-light/70">Ask me about Aubrey's work and expertise</p>
          </div>
          <div className="ml-auto">
            <Sparkles className="text-matrix-primary animate-pulse" size={16} />
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.sender === 'ai' && (
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-matrix-primary to-matrix-accent flex items-center justify-center flex-shrink-0">
                  <Bot size={16} className="text-matrix-dark" />
                </div>
              )}
              
              <div className={`max-w-[80%] p-3 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-gradient-to-r from-matrix-primary to-matrix-accent text-matrix-dark'
                  : 'bg-matrix-medium border border-matrix-primary/20'
              }`}>
                <p className="text-sm leading-relaxed">{message.content}</p>
                <p className={`text-xs mt-1 ${
                  message.sender === 'user' ? 'text-matrix-dark/70' : 'text-matrix-light/50'
                }`}>
                  {message.timestamp.toLocaleTimeString()}
                </p>
              </div>

              {message.sender === 'user' && (
                <div className="w-8 h-8 rounded-full bg-matrix-light flex items-center justify-center flex-shrink-0">
                  <User size={16} className="text-matrix-dark" />
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Typing indicator */}
        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex gap-3"
          >
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-matrix-primary to-matrix-accent flex items-center justify-center">
              <Bot size={16} className="text-matrix-dark" />
            </div>
            <div className="bg-matrix-medium border border-matrix-primary/20 p-3 rounded-lg">
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-matrix-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-matrix-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-matrix-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Quick Questions */}
      {messages.length === 1 && (
        <div className="p-4 border-t border-matrix-primary/20">
          <p className="text-xs text-matrix-light/70 mb-2">Quick questions:</p>
          <div className="flex flex-wrap gap-2">
            {quickQuestions.map((question, index) => (
              <button
                key={index}
                onClick={() => setInput(question)}
                className="text-xs px-3 py-1 bg-matrix-medium border border-matrix-primary/30 rounded-full hover:border-matrix-primary hover:bg-matrix-primary/10 transition-all"
              >
                {question}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t border-matrix-primary/20 bg-matrix-medium">
        <div className="flex gap-2">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything about Aubrey's work..."
            className="flex-1 bg-matrix-surface border border-matrix-primary/30 rounded-lg px-3 py-2 text-sm resize-none focus:outline-none focus:border-matrix-primary"
            rows={1}
            style={{ minHeight: '40px', maxHeight: '120px' }}
            disabled={isTyping}
          />
          <button
            onClick={handleSend}
            disabled={!input.trim() || isTyping}
            className="w-10 h-10 bg-gradient-to-r from-matrix-primary to-matrix-accent text-matrix-dark rounded-lg flex items-center justify-center hover:shadow-lg hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send size={16} />
          </button>
        </div>
      </div>
    </div>
  )
}