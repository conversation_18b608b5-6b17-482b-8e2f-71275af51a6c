'use client'\n\nimport { createContext, useContext, useState, useCallback, ReactNode } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { generateId } from '@/lib/utils'\n\ninterface Notification {\n  id: string\n  title: string\n  message: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  duration?: number\n  autoHide?: boolean\n}\n\ninterface NotificationContextType {\n  notifications: Notification[]\n  addNotification: (notification: Omit<Notification, 'id'>) => void\n  removeNotification: (id: string) => void\n  clearAll: () => void\n}\n\nconst NotificationContext = createContext<NotificationContextType | null>(null)\n\nexport function useNotifications() {\n  const context = useContext(NotificationContext)\n  if (!context) {\n    throw new Error('useNotifications must be used within a NotificationProvider')\n  }\n  return context\n}\n\ninterface NotificationProviderProps {\n  children: ReactNode\n}\n\nexport function NotificationProvider({ children }: NotificationProviderProps) {\n  const [notifications, setNotifications] = useState<Notification[]>([])\n\n  const addNotification = useCallback((notification: Omit<Notification, 'id'>) => {\n    const id = generateId()\n    const newNotification: Notification = {\n      id,\n      autoHide: true,\n      duration: 5000,\n      ...notification,\n    }\n\n    setNotifications(prev => [...prev, newNotification])\n\n    if (newNotification.autoHide) {\n      setTimeout(() => {\n        removeNotification(id)\n      }, newNotification.duration)\n    }\n  }, [])\n\n  const removeNotification = useCallback((id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id))\n  }, [])\n\n  const clearAll = useCallback(() => {\n    setNotifications([])\n  }, [])\n\n  return (\n    <NotificationContext.Provider value={{\n      notifications,\n      addNotification,\n      removeNotification,\n      clearAll\n    }}>\n      {children}\n      <NotificationContainer />\n    </NotificationContext.Provider>\n  )\n}\n\nfunction NotificationContainer() {\n  const { notifications, removeNotification } = useNotifications()\n\n  const getIcon = (type: Notification['type']) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"w-5 h-5\" />\n      case 'error':\n        return <AlertCircle className=\"w-5 h-5\" />\n      case 'warning':\n        return <AlertTriangle className=\"w-5 h-5\" />\n      case 'info':\n        return <Info className=\"w-5 h-5\" />\n    }\n  }\n\n  const getColors = (type: Notification['type']) => {\n    switch (type) {\n      case 'success':\n        return 'border-green-500/50 bg-green-500/10 text-green-400'\n      case 'error':\n        return 'border-red-500/50 bg-red-500/10 text-red-400'\n      case 'warning':\n        return 'border-yellow-500/50 bg-yellow-500/10 text-yellow-400'\n      case 'info':\n        return 'border-blue-500/50 bg-blue-500/10 text-blue-400'\n    }\n  }\n\n  return (\n    <div className=\"fixed top-4 right-4 z-[9999] space-y-2 max-w-sm\">\n      <AnimatePresence>\n        {notifications.map((notification) => (\n          <motion.div\n            key={notification.id}\n            initial={{ opacity: 0, x: 300, scale: 0.8 }}\n            animate={{ opacity: 1, x: 0, scale: 1 }}\n            exit={{ opacity: 0, x: 300, scale: 0.8 }}\n            transition={{ duration: 0.3, ease: 'easeOut' }}\n            className={cn(\n              \"p-4 rounded-lg border backdrop-blur-sm shadow-lg\",\n              getColors(notification.type)\n            )}\n          >\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0 mt-0.5\">\n                {getIcon(notification.type)}\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <h4 className=\"text-sm font-semibold mb-1\">\n                  {notification.title}\n                </h4>\n                <p className=\"text-sm opacity-90\">\n                  {notification.message}\n                </p>\n              </div>\n              <button\n                onClick={() => removeNotification(notification.id)}\n                className=\"flex-shrink-0 p-1 rounded hover:bg-white/10 transition-colors\"\n              >\n                <X className=\"w-4 h-4\" />\n              </button>\n            </div>\n          </motion.div>\n        ))}\n      </AnimatePresence>\n    </div>\n  )\n}