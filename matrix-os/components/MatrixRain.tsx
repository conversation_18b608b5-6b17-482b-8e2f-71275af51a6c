'use client'

import { useEffect, useRef, useCallback } from 'react'
import { getRandomMatrixChar, throttle } from '@/lib/utils'

interface MatrixRainProps {
  className?: string
  opacity?: number
  speed?: number
  fontSize?: number
}

export default function MatrixRain({
  className = '',
  opacity = 0.6,
  speed = 30,
  fontSize = 14,
}: MatrixRainProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const columnsRef = useRef<number[]>([])
  const lastTimeRef = useRef<number>(0)

  const resizeCanvas = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    canvas.width = window.innerWidth
    canvas.height = window.innerHeight

    const columns = Math.floor(canvas.width / fontSize)
    columnsRef.current = Array(columns).fill(1)
  }, [fontSize])

  const draw = useCallback((currentTime: number) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Throttle the animation to the specified speed
    if (currentTime - lastTimeRef.current < speed) {
      animationRef.current = requestAnimationFrame(draw)
      return
    }

    lastTimeRef.current = currentTime

    // Create fade effect
    ctx.fillStyle = 'rgba(0, 0, 0, 0.05)'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Set matrix text style
    ctx.fillStyle = '#00ff00'
    ctx.font = `${fontSize}px 'Share Tech Mono', monospace`

    // Draw the falling characters
    for (let i = 0; i < columnsRef.current.length; i++) {
      const text = getRandomMatrixChar()
      const x = i * fontSize
      const y = columnsRef.current[i] * fontSize

      ctx.fillText(text, x, y)

      // Randomly reset column when it reaches bottom
      if (y > canvas.height && Math.random() > 0.975) {
        columnsRef.current[i] = 0
      }

      columnsRef.current[i]++
    }

    animationRef.current = requestAnimationFrame(draw)
  }, [fontSize, speed])

  const throttledResize = useCallback(
    throttle(() => {
      resizeCanvas()
    }, 250),
    [resizeCanvas]
  )

  useEffect(() => {
    resizeCanvas()
    
    const startAnimation = () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      animationRef.current = requestAnimationFrame(draw)
    }

    startAnimation()

    window.addEventListener('resize', throttledResize)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      window.removeEventListener('resize', throttledResize)
    }
  }, [draw, throttledResize, resizeCanvas])

  return (
    <canvas
      ref={canvasRef}
      className={`fixed top-0 left-0 w-full h-full pointer-events-none ${className}`}
      style={{
        zIndex: -1,
        opacity,
      }}
    />
  )
}