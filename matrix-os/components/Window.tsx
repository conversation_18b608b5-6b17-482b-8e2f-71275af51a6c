'use client'

import { useEffect, useRef, useState, memo } from 'react'
import Draggable from 'react-draggable'
import { WindowState } from '@/types'
import { cn } from '@/lib/utils'
import { Minus, Square, X, Maximize2, Minimize2 } from 'lucide-react'

interface WindowProps {
  window: WindowState
  isActive: boolean
  onClose: () => void
  onMinimize: () => void
  onMaximize: () => void
  onFocus: () => void
  onPositionChange: (position: { x: number; y: number }) => void
  onSizeChange: (size: { width: number; height: number }) => void
  onSnap?: (zone: any) => void
  children: React.ReactNode
}

const Window = memo(function Window({
  window,
  isActive,
  onClose,
  onMinimize,
  onMaximize,
  onFocus,
  onPositionChange,
  onSizeChange,
  onSnap,
  children,
}: WindowProps) {
  const nodeRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [snapPreview, setSnapPreview] = useState<any>(null)

  useEffect(() => {
    if (isActive && nodeRef.current) {
      nodeRef.current.focus()
    }
  }, [isActive])

  // Handle drag events for snapping
  const handleDragStart = () => {
    setIsDragging(true)
  }

  const handleDrag = (e: any, data: any) => {
    onPositionChange({ x: data.x, y: data.y })
    
    // Check for snap zones
    if (onSnap) {
      const threshold = 50
      const { x, y } = data
      
      let preview = null
      if (x < threshold) {
        preview = { type: 'left', x: 0, y: 0, width: window.innerWidth / 2, height: window.innerHeight - 48 }
      } else if (x > window.innerWidth - threshold) {
        preview = { type: 'right', x: window.innerWidth / 2, y: 0, width: window.innerWidth / 2, height: window.innerHeight - 48 }
      } else if (y < threshold) {
        preview = { type: 'center', x: 0, y: 0, width: window.innerWidth, height: window.innerHeight - 48 }
      }
      
      setSnapPreview(preview)
    }
  }

  const handleDragStop = (e: any, data: any) => {
    setIsDragging(false)
    
    if (snapPreview && onSnap) {
      onSnap(snapPreview)
    }
    setSnapPreview(null)
  }

  if (window.isMinimized) return null

  const windowStyle = window.isMaximized
    ? {
        position: 'fixed' as const,
        top: 0,
        left: 0,
        width: '100vw',
        height: 'calc(100vh - 48px)', // Account for taskbar
        zIndex: window.zIndex,
      }
    : {
        position: 'absolute' as const,
        zIndex: window.zIndex,
      }

  const WindowContent = (
    <div
      ref={nodeRef}
      className={cn(
        'os-window select-none outline-none transition-all duration-200',
        isActive && 'ring-1 ring-matrix-primary/50',
        isDragging && 'shadow-matrix-xl scale-[1.02]'
      )}
      style={windowStyle}
      onMouseDown={onFocus}
      tabIndex={-1}
    >
      {/* Window Header */}
      <div className="window-header os-window-header flex items-center justify-between px-4 py-3 cursor-move bg-gradient-to-r from-matrix-surface/80 to-matrix-surface/60 backdrop-blur-sm border-b border-matrix-primary/20">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="w-3 h-3 rounded-full bg-red-500/80 hover:bg-red-500 transition-colors cursor-pointer flex items-center justify-center"
              title="Close"
            >
              <X className="w-2 h-2 text-red-900 opacity-0 hover:opacity-100 transition-opacity" />
            </button>
            <button
              onClick={onMinimize}
              className="w-3 h-3 rounded-full bg-yellow-500/80 hover:bg-yellow-500 transition-colors cursor-pointer flex items-center justify-center"
              title="Minimize"
            >
              <Minus className="w-2 h-2 text-yellow-900 opacity-0 hover:opacity-100 transition-opacity" />
            </button>
            <button
              onClick={onMaximize}
              className="w-3 h-3 rounded-full bg-green-500/80 hover:bg-green-500 transition-colors cursor-pointer flex items-center justify-center"
              title={window.isMaximized ? "Restore" : "Maximize"}
            >
              {window.isMaximized ? (
                <Square className="w-1.5 h-1.5 text-green-900 opacity-0 hover:opacity-100 transition-opacity" />
              ) : (
                <Maximize2 className="w-1.5 h-1.5 text-green-900 opacity-0 hover:opacity-100 transition-opacity" />
              )}
            </button>
          </div>
          <div className="w-px h-4 bg-matrix-primary/20" />
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 rounded-sm bg-matrix-primary/20 flex items-center justify-center">
              <div className="w-2 h-2 rounded-xs bg-matrix-primary/60" />
            </div>
            <span className="text-sm font-display text-matrix-light font-medium truncate max-w-xs tracking-wide">
              {window.title}
            </span>
          </div>
        </div>
      </div>

      {/* Window Content */}
      <div className="flex-1 overflow-hidden os-window-content bg-gradient-to-br from-matrix-dark/95 to-matrix-surface/95 backdrop-blur-sm">
        {children}
      </div>
    </div>
  )

  // Snap preview overlay
  const SnapPreview = snapPreview && (
    <div
      className="fixed pointer-events-none bg-matrix-primary/20 border-2 border-matrix-primary/50 rounded-lg transition-all duration-200 z-[9999]"
      style={{
        left: snapPreview.x,
        top: snapPreview.y,
        width: snapPreview.width,
        height: snapPreview.height,
      }}
    />
  )

  if (window.isMaximized) {
    return (
      <>
        {WindowContent}
        {SnapPreview}
      </>
    )
  }

  return (
    <>
      <Draggable
        nodeRef={nodeRef}
        disabled={!window.isDraggable}
        handle=".window-header"
        position={window.position}
        onStart={handleDragStart}
        onDrag={handleDrag}
        onStop={handleDragStop}
      >
        <div
          style={{
            width: window.size.width,
            height: window.size.height,
          }}
        >
          {WindowContent}
        </div>
      </Draggable>
      {SnapPreview}
    </>
  )
})

export default Window