'use client'

import React, { useState, useEffect } from 'react'
import { WindowState } from '@/types'
import { formatTime } from '@/lib/utils'
import { Monitor, Volume2, Wifi, Battery, User, Settings, Power, Menu, VolumeX, Volume1, WifiOff } from 'lucide-react'

interface TaskbarProps {
  windows: WindowState[]
  activeWindowId: string | null
  onWindowRestore: (id: string) => void
  onWindowFocus: (id: string) => void
}

export default function Taskbar({
  windows,
  activeWindowId,
  onWindowRestore,
  onWindowFocus,
}: TaskbarProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [systemStats, setSystemStats] = useState({
    cpu: 0,
    memory: 0,
    network: true,
    battery: 100,
  })
  const [showStartMenu, setShowStartMenu] = useState(false)
  const [showVolumeMenu, setShowVolumeMenu] = useState(false)
  const [showNetworkMenu, setShowNetworkMenu] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [volume, setVolume] = useState(75)
  const [isMuted, setIsMuted] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
      // Simulate system stats
      setSystemStats(prev => ({
        ...prev,
        cpu: Math.floor(Math.random() * 15) + 5,
        memory: Math.floor(Math.random() * 20) + 30,
      }))
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const handleWindowClick = (window: WindowState) => {
    if (window.isMinimized) {
      onWindowRestore(window.id)
    } else {
      onWindowFocus(window.id)
    }
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    if (newVolume === 0) {
      setIsMuted(true)
    } else if (isMuted) {
      setIsMuted(false)
    }
  }

  const toggleMute = () => {
    setIsMuted(!isMuted)
  }

  const getVolumeIcon = () => {
    if (isMuted || volume === 0) return VolumeX
    if (volume < 50) return Volume1
    return Volume2
  }

  const closeAllMenus = () => {
    setShowStartMenu(false)
    setShowVolumeMenu(false)
    setShowNetworkMenu(false)
    setShowUserMenu(false)
  }

  return (
    <React.Fragment>
      {/* Overlay to close menus when clicking outside */}
      {(showStartMenu || showVolumeMenu || showNetworkMenu || showUserMenu) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={closeAllMenus}
        />
      )}
      
      <div className="fixed bottom-0 left-0 right-0 h-14 taskbar z-50 flex items-center justify-between px-6">
        {/* Start Menu / Logo */}
        <div className="flex items-center space-x-3 relative">
          <button 
            onClick={() => {
              closeAllMenus()
              setShowStartMenu(!showStartMenu)
            }}
            className={`taskbar-item group flex items-center space-x-2 focus-ring ${showStartMenu ? 'active' : ''}`}
          >
            <div className="relative">
              <Monitor className="w-6 h-6 text-matrix-primary transition-all duration-300 group-hover:scale-110 group-hover:text-matrix-accent" />
              <div className="absolute inset-0 bg-matrix-primary/20 rounded-full scale-0 group-hover:scale-150 transition-transform duration-500 opacity-0 group-hover:opacity-100" />
            </div>
            <div className="text-sm font-display text-matrix-light font-semibold tracking-wider group-hover:text-matrix-primary transition-colors duration-300">
              MATRIX OS
            </div>
          </button>
          
          {/* Start Menu Dropdown */}
          {showStartMenu && (
            <div className="absolute bottom-full left-0 mb-2 w-80 bg-matrix-surface/95 backdrop-blur-xl border border-matrix-primary/30 rounded-xl shadow-matrix-lg">
              <div className="p-4">
                <div className="text-matrix-primary font-display font-semibold text-lg mb-4">System Menu</div>
                <div className="space-y-2">
                  <button className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-matrix-primary/15 transition-colors text-left">
                    <Settings className="w-5 h-5 text-matrix-primary" />
                    <span className="text-matrix-light">Settings</span>
                  </button>
                  <button className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-matrix-primary/15 transition-colors text-left">
                    <User className="w-5 h-5 text-matrix-primary" />
                    <span className="text-matrix-light">User Profile</span>
                  </button>
                  <div className="border-t border-matrix-primary/20 my-2"></div>
                  <button className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-red-500/20 transition-colors text-left group">
                    <Power className="w-5 h-5 text-red-400 group-hover:text-red-300" />
                    <span className="text-matrix-light group-hover:text-red-300">Shutdown</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Window Tasks */}
        <div className="flex-1 flex items-center justify-center space-x-2 px-6">
          {windows.map((window) => (
            <button
              key={window.id}
              onClick={() => handleWindowClick(window)}
              className={`
                taskbar-item relative px-4 py-2 rounded-lg text-sm font-display font-medium transition-all duration-400 ease-spring focus-ring
                ${window.id === activeWindowId && !window.isMinimized
                  ? 'active bg-matrix-primary/25 text-matrix-primary border border-matrix-primary/40 shadow-matrix-md'
                  : 'bg-matrix-surface/40 text-matrix-light hover:bg-matrix-primary/15 border border-matrix-primary/20 hover:border-matrix-primary/30'
                }
                ${window.isMinimized ? 'opacity-60 scale-95' : ''}
              `}
              title={window.title}
            >
              <span className="relative z-10 max-w-36 truncate block tracking-wide">
                {window.title}
              </span>
              {window.id === activeWindowId && !window.isMinimized && (
                <div className="absolute inset-0 bg-gradient-to-r from-matrix-primary/10 to-matrix-primary/5 rounded-lg animate-glow-pulse" />
              )}
            </button>
          ))}
        </div>

        {/* System Tray */}
        <div className="flex items-center space-x-4">
          {/* System Stats */}
          <div className="flex items-center space-x-3 text-xs font-mono text-matrix-light bg-matrix-surface/30 px-3 py-2 rounded-lg border border-matrix-primary/20 backdrop-blur-md">
            <div className="flex items-center space-x-1.5 group cursor-pointer" title="CPU Usage">
              <div className="status-indicator online" />
              <span className="group-hover:text-matrix-primary transition-colors">CPU: {systemStats.cpu}%</span>
            </div>
            <div className="w-px h-3 bg-matrix-primary/30" />
            <div className="flex items-center space-x-1.5 group cursor-pointer" title="Memory Usage">
              <div className="w-2 h-2 bg-matrix-accent rounded-full shadow-[0_0_4px_rgba(64,255,138,0.5)]" />
              <span className="group-hover:text-matrix-primary transition-colors">MEM: {systemStats.memory}%</span>
            </div>
          </div>

          {/* System Icons */}
          <div className="flex items-center space-x-1 relative">
            {/* Volume Control */}
            <div className="relative">
              <button 
                onClick={() => {
                  closeAllMenus()
                  setShowVolumeMenu(!showVolumeMenu)
                }}
                className={`p-2 rounded-lg hover:bg-matrix-primary/15 focus-visible:bg-matrix-primary/20 transition-all duration-300 ease-spring group focus-ring ${showVolumeMenu ? 'bg-matrix-primary/20' : ''}`}
                title="Volume Control"
              >
                {React.createElement(getVolumeIcon(), {
                  className: "w-4 h-4 text-matrix-light group-hover:text-matrix-primary group-hover:scale-110 transition-all duration-300"
                })}
              </button>
              {showVolumeMenu && (
                <div className="absolute bottom-full right-0 mb-2 w-48 bg-matrix-surface/95 backdrop-blur-xl border border-matrix-primary/30 rounded-xl shadow-matrix-lg p-4">
                  <div className="text-matrix-primary font-medium mb-3">Volume Control</div>
                  <div className="space-y-3">
                    <button 
                      onClick={toggleMute}
                      className="w-full flex items-center justify-between px-3 py-2 rounded-lg hover:bg-matrix-primary/15 transition-colors"
                    >
                      <span className="text-matrix-light text-sm">Mute</span>
                      <div className={`w-4 h-4 rounded-full ${isMuted ? 'bg-red-500' : 'bg-matrix-primary'} transition-colors`} />
                    </button>
                    <div className="space-y-2">
                      <label className="text-matrix-light text-sm">Volume: {volume}%</label>
                      <input 
                        type="range" 
                        min="0" 
                        max="100" 
                        value={volume}
                        onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
                        className="w-full h-2 bg-matrix-surface rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Network Status */}
            <div className="relative">
              <button 
                onClick={() => {
                  closeAllMenus()
                  setShowNetworkMenu(!showNetworkMenu)
                }}
                className={`p-2 rounded-lg hover:bg-matrix-primary/15 focus-visible:bg-matrix-primary/20 transition-all duration-300 ease-spring group focus-ring ${showNetworkMenu ? 'bg-matrix-primary/20' : ''}`}
                title="Network Status"
              >
                {systemStats.network ? (
                  <Wifi className="w-4 h-4 text-matrix-primary group-hover:text-matrix-accent group-hover:scale-110 transition-all duration-300" />
                ) : (
                  <WifiOff className="w-4 h-4 text-red-400 group-hover:text-red-300 group-hover:scale-110 transition-all duration-300" />
                )}
              </button>
              {showNetworkMenu && (
                <div className="absolute bottom-full right-0 mb-2 w-56 bg-matrix-surface/95 backdrop-blur-xl border border-matrix-primary/30 rounded-xl shadow-matrix-lg p-4">
                  <div className="text-matrix-primary font-medium mb-3">Network Status</div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-matrix-light text-sm">Connection</span>
                      <span className={`text-sm ${systemStats.network ? 'text-matrix-primary' : 'text-red-400'}`}>
                        {systemStats.network ? 'Connected' : 'Disconnected'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-matrix-light text-sm">Signal</span>
                      <span className="text-matrix-primary text-sm">Excellent</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-matrix-light text-sm">Speed</span>
                      <span className="text-matrix-primary text-sm">1000 Mbps</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Battery Status */}
            <button className="p-2 rounded-lg hover:bg-matrix-primary/15 focus-visible:bg-matrix-primary/20 transition-all duration-300 ease-spring group focus-ring" title={`Battery: ${systemStats.battery}%`}>
              <Battery className="w-4 h-4 text-matrix-accent group-hover:text-matrix-primary group-hover:scale-110 transition-all duration-300" />
            </button>

            {/* User Account */}
            <div className="relative">
              <button 
                onClick={() => {
                  closeAllMenus()
                  setShowUserMenu(!showUserMenu)
                }}
                className={`p-2 rounded-lg hover:bg-matrix-primary/15 focus-visible:bg-matrix-primary/20 transition-all duration-300 ease-spring group focus-ring ${showUserMenu ? 'bg-matrix-primary/20' : ''}`}
                title="User Account"
              >
                <User className="w-4 h-4 text-matrix-light group-hover:text-matrix-primary group-hover:scale-110 transition-all duration-300" />
              </button>
              {showUserMenu && (
                <div className="absolute bottom-full right-0 mb-2 w-48 bg-matrix-surface/95 backdrop-blur-xl border border-matrix-primary/30 rounded-xl shadow-matrix-lg p-4">
                  <div className="text-matrix-primary font-medium mb-3">User Account</div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-matrix-primary/15 transition-colors cursor-pointer">
                      <User className="w-4 h-4 text-matrix-primary" />
                      <span className="text-matrix-light text-sm">Neo</span>
                    </div>
                    <div className="border-t border-matrix-primary/20 my-2"></div>
                    <button className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-matrix-primary/15 transition-colors text-left">
                      <Settings className="w-4 h-4 text-matrix-primary" />
                      <span className="text-matrix-light text-sm">Account Settings</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-red-500/20 transition-colors text-left group">
                      <Power className="w-4 h-4 text-red-400 group-hover:text-red-300" />
                      <span className="text-matrix-light group-hover:text-red-300 text-sm">Sign Out</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Clock */}
          <div className="text-sm font-display text-matrix-primary bg-gradient-to-br from-matrix-surface/50 to-matrix-surface/30 px-4 py-2 rounded-lg border border-matrix-primary/30 backdrop-blur-md shadow-matrix-sm hover:shadow-matrix-md transition-all duration-300">
            <div className="text-center">
              <div className="leading-none font-semibold tracking-wider">{formatTime(currentTime)}</div>
              <div className="text-xs text-matrix-light/80 leading-none mt-0.5 font-medium">
                {currentTime.toLocaleDateString('en-US', { 
                  month: 'short', 
                  day: 'numeric' 
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </React.Fragment>
  )
}