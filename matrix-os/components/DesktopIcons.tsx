'use client'

import { motion } from 'framer-motion'
import { DesktopIcon as DesktopIconType } from '@/types'
import { 
  Terminal, 
  User, 
  FileText, 
  MessageSquare, 
  Folder,
  Settings,
  Info,
  Code,
  Globe
} from 'lucide-react'

interface DesktopIconsProps {
  icons: DesktopIconType[]
  onIconClick: (icon: DesktopIconType) => void
}

const iconComponents = {
  terminal: Terminal,
  user: User,
  file: FileText,
  chat: MessageSquare,
  folder: Folder,
  settings: Settings,
  info: Info,
  code: Code,
  globe: Globe,
}

export default function DesktopIcons({ icons, onIconClick }: DesktopIconsProps) {
  return (
    <div className="absolute inset-0 p-8 pb-20">
      {icons.map((icon, index) => {
        const IconComponent = iconComponents[icon.icon as keyof typeof iconComponents] || FileText

        return (
          <motion.button
            key={icon.id}
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ 
              delay: index * 0.08,
              type: "spring",
              stiffness: 300,
              damping: 25
            }}
            className="desktop-icon absolute focus-ring"
            style={{
              left: icon.position.x,
              top: icon.position.y,
            }}
            onClick={() => onIconClick(icon)}
            whileHover={{ 
              scale: 1.08,
              transition: { type: "spring", stiffness: 400, damping: 10 }
            }}
            whileTap={{ 
              scale: 0.92,
              transition: { type: "spring", stiffness: 600, damping: 15 }
            }}
          >
            <div className="desktop-icon-image relative group">
              <div className="w-16 h-16 flex items-center justify-center mb-2 rounded-2xl bg-gradient-to-br from-matrix-surface/60 to-matrix-medium/40 backdrop-blur-md border border-matrix-primary/25 shadow-matrix-md transition-all duration-400 ease-spring group-hover:shadow-matrix-lg group-hover:border-matrix-primary/40">
                {/* Background glow effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-matrix-primary/5 to-matrix-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-400" />
                
                {/* Icon */}
                <IconComponent className="w-8 h-8 text-matrix-primary relative z-10 transition-all duration-300 group-hover:scale-110 group-hover:text-matrix-accent" />
                
                {/* Subtle shine effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                
                {/* Status indicator for interactive icons */}
                {icon.icon === 'terminal' && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-matrix-dark shadow-[0_0_6px_rgba(0,255,65,0.6)] animate-pulse" />
                )}
                
                {icon.icon === 'chat' && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-matrix-dark shadow-[0_0_6px_rgba(59,130,246,0.6)]" />
                )}
              </div>
            </div>
            
            <div className="desktop-icon-label text-xs font-sans text-matrix-light text-center max-w-20 leading-tight font-medium px-1">
              <span className="block truncate">
                {icon.name}
              </span>
            </div>
            
            {/* Selection indicator */}
            <div className="absolute inset-0 rounded-xl border-2 border-matrix-primary/0 transition-all duration-300 hover:border-matrix-primary/30 focus-visible:border-matrix-primary/50 -m-1" />
          </motion.button>
        )
      })}
    </div>
  )
}