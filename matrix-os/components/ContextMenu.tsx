'use client'\n\nimport { createContext, useContext, useState, useCallback, ReactNode } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface ContextMenuItem {\n  id: string\n  label: string\n  icon?: ReactNode\n  onClick: () => void\n  disabled?: boolean\n  separator?: boolean\n}\n\ninterface ContextMenuState {\n  isOpen: boolean\n  position: { x: number; y: number }\n  items: ContextMenuItem[]\n}\n\ninterface ContextMenuContextType {\n  showContextMenu: (x: number, y: number, items: ContextMenuItem[]) => void\n  hideContextMenu: () => void\n  contextMenu: ContextMenuState\n}\n\nconst ContextMenuContext = createContext<ContextMenuContextType | null>(null)\n\nexport function useContextMenu() {\n  const context = useContext(ContextMenuContext)\n  if (!context) {\n    throw new Error('useContextMenu must be used within a ContextMenuProvider')\n  }\n  return context\n}\n\ninterface ContextMenuProviderProps {\n  children: ReactNode\n}\n\nexport function ContextMenuProvider({ children }: ContextMenuProviderProps) {\n  const [contextMenu, setContextMenu] = useState<ContextMenuState>({\n    isOpen: false,\n    position: { x: 0, y: 0 },\n    items: []\n  })\n\n  const showContextMenu = useCallback((x: number, y: number, items: ContextMenuItem[]) => {\n    setContextMenu({\n      isOpen: true,\n      position: { x, y },\n      items\n    })\n  }, [])\n\n  const hideContextMenu = useCallback(() => {\n    setContextMenu(prev => ({ ...prev, isOpen: false }))\n  }, [])\n\n  return (\n    <ContextMenuContext.Provider value={{ showContextMenu, hideContextMenu, contextMenu }}>\n      {children}\n      <ContextMenuOverlay />\n    </ContextMenuContext.Provider>\n  )\n}\n\nfunction ContextMenuOverlay() {\n  const { contextMenu, hideContextMenu } = useContextMenu()\n\n  return (\n    <AnimatePresence>\n      {contextMenu.isOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-[10000]\"\n            onClick={hideContextMenu}\n            onContextMenu={(e) => {\n              e.preventDefault()\n              hideContextMenu()\n            }}\n          />\n          \n          {/* Context Menu */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.95 }}\n            transition={{ duration: 0.1 }}\n            className=\"fixed z-[10001] bg-matrix-surface/95 backdrop-blur-md border border-matrix-primary/30 rounded-lg shadow-matrix-lg min-w-48 py-2\"\n            style={{\n              left: Math.min(contextMenu.position.x, window.innerWidth - 200),\n              top: Math.min(contextMenu.position.y, window.innerHeight - contextMenu.items.length * 40 - 20)\n            }}\n          >\n            {contextMenu.items.map((item, index) => (\n              <div key={item.id}>\n                {item.separator && (\n                  <div className=\"h-px bg-matrix-primary/20 mx-2 my-1\" />\n                )}\n                <button\n                  onClick={() => {\n                    if (!item.disabled) {\n                      item.onClick()\n                      hideContextMenu()\n                    }\n                  }}\n                  disabled={item.disabled}\n                  className={cn(\n                    \"w-full flex items-center space-x-3 px-4 py-2 text-left transition-colors\",\n                    item.disabled\n                      ? \"text-matrix-light/40 cursor-not-allowed\"\n                      : \"text-matrix-light hover:bg-matrix-primary/20 hover:text-matrix-primary\"\n                  )}\n                >\n                  {item.icon && (\n                    <span className=\"w-4 h-4 flex-shrink-0\">{item.icon}</span>\n                  )}\n                  <span className=\"text-sm\">{item.label}</span>\n                </button>\n              </div>\n            ))}\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  )\n}