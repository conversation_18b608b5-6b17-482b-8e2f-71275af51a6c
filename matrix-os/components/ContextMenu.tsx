'use client'

import { createContext, useContext, useState, useC<PERSON>back, ReactNode } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

interface ContextMenuItem {
  id: string
  label: string
  icon?: ReactNode
  onClick: () => void
  disabled?: boolean
  separator?: boolean
}

interface ContextMenuState {
  isOpen: boolean
  position: { x: number; y: number }
  items: ContextMenuItem[]
}

interface ContextMenuContextType {
  showContextMenu: (x: number, y: number, items: ContextMenuItem[]) => void
  hideContextMenu: () => void
  contextMenu: ContextMenuState
}

const ContextMenuContext = createContext<ContextMenuContextType | null>(null)

export function useContextMenu() {
  const context = useContext(ContextMenuContext)
  if (!context) {
    throw new Error('useContextMenu must be used within a ContextMenuProvider')
  }
  return context
}

interface ContextMenuProviderProps {
  children: ReactNode
}

export function ContextMenuProvider({ children }: ContextMenuProviderProps) {
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    isOpen: false,
    position: { x: 0, y: 0 },
    items: []
  })

  const showContextMenu = useCallback((x: number, y: number, items: ContextMenuItem[]) => {
    setContextMenu({
      isOpen: true,
      position: { x, y },
      items
    })
  }, [])

  const hideContextMenu = useCallback(() => {
    setContextMenu(prev => ({ ...prev, isOpen: false }))
  }, [])

  return (
    <ContextMenuContext.Provider value={{ showContextMenu, hideContextMenu, contextMenu }}>
      {children}
      <ContextMenuOverlay />
    </ContextMenuContext.Provider>
  )
}

function ContextMenuOverlay() {
  const { contextMenu, hideContextMenu } = useContextMenu()

  return (
    <AnimatePresence>
      {contextMenu.isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-[10000]"
            onClick={hideContextMenu}
            onContextMenu={(e) => {
              e.preventDefault()
              hideContextMenu()
            }}
          />

          {/* Context Menu */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.1 }}
            className="fixed z-[10001] bg-matrix-surface/95 backdrop-blur-md border border-matrix-primary/30 rounded-lg shadow-matrix-lg min-w-48 py-2"
            style={{
              left: Math.min(contextMenu.position.x, window.innerWidth - 200),
              top: Math.min(contextMenu.position.y, window.innerHeight - contextMenu.items.length * 40 - 20)
            }}
          >
            {contextMenu.items.map((item, index) => (
              <div key={item.id}>
                {item.separator && (
                  <div className="h-px bg-matrix-primary/20 mx-2 my-1" />
                )}
                <button
                  onClick={() => {
                    if (!item.disabled) {
                      item.onClick()
                      hideContextMenu()
                    }
                  }}
                  disabled={item.disabled}
                  className={cn(
                    "w-full flex items-center space-x-3 px-4 py-2 text-left transition-colors",
                    item.disabled
                      ? "text-matrix-light/40 cursor-not-allowed"
                      : "text-matrix-light hover:bg-matrix-primary/20 hover:text-matrix-primary"
                  )}
                >
                  {item.icon && (
                    <span className="w-4 h-4 flex-shrink-0">{item.icon}</span>
                  )}
                  <span className="text-sm">{item.label}</span>
                </button>
              </div>
            ))}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}