'use client'

import { useState, lazy, Suspense } from 'react'
import { motion } from 'framer-motion'
import { useWindowManager } from '@/hooks/useWindowManager'
import { DesktopIcon } from '@/types'
import { desktopIcons } from '@/config/desktop-icons'
import { ContextMenuProvider, useContextMenu } from './ContextMenu'
import { NotificationProvider, useNotifications } from './NotificationSystem'
import { Terminal, Folder, Settings, RefreshCw, Trash2 } from 'lucide-react'
import MatrixRain from './MatrixRain'
import Window from './Window'
import Taskbar from './Taskbar'
import DesktopIcons from './DesktopIcons'

// Lazy load components for better performance
const TerminalApp = lazy(() => import('./apps/Terminal'))
const About = lazy(() => import('./apps/About'))
const Chat = lazy(() => import('./apps/Chat'))
const FileExplorer = lazy(() => import('./apps/FileExplorer'))
const Portfolio = lazy(() => import('./apps/Portfolio'))
const SettingsApp = lazy(() => import('./apps/Settings'))

const componentMap = {
  Terminal: TerminalApp,
  About,
  Chat,
  FileExplorer,
  Portfolio,
  Settings: SettingsApp,
}

function DesktopContent() {
  const windowManager = useWindowManager()
  const { showContextMenu } = useContextMenu()
  const { addNotification } = useNotifications()
  const [bootComplete, setBootComplete] = useState(false)

  const handleIconClick = (icon: DesktopIcon) => {
    // Check if window is already open
    const existingWindow = windowManager.windows.find(w => w.component === icon.component)
    
    if (existingWindow) {
      if (existingWindow.isMinimized) {
        windowManager.restoreWindow(existingWindow.id)
      } else {
        windowManager.focusWindow(existingWindow.id)
      }
    } else {
      windowManager.createWindow(
        icon.name,
        icon.component,
        icon.windowProps
      )
      
      // Show notification
      addNotification({
        title: 'Application Launched',
        message: `${icon.name} has been opened successfully.`,
        type: 'success'
      })
    }
  }

  const handleDesktopContextMenu = (e: React.MouseEvent) => {
    e.preventDefault()
    
    showContextMenu(e.clientX, e.clientY, [
      {
        id: 'new-folder',
        label: 'New Folder',
        icon: <Folder className="w-4 h-4" />,
        onClick: () => {
          addNotification({
            title: 'Feature Coming Soon',
            message: 'File system functionality will be available in the next update.',
            type: 'info'
          })
        }
      },
      {
        id: 'terminal',
        label: 'Open Terminal',
        icon: <Terminal className="w-4 h-4" />,
        onClick: () => {
          const terminalIcon = desktopIcons.find(icon => icon.component === 'Terminal')
          if (terminalIcon) handleIconClick(terminalIcon)
        }
      },
      {
        id: 'separator-1',
        label: '',
        separator: true,
        onClick: () => {}
      },
      {
        id: 'refresh',
        label: 'Refresh Desktop',
        icon: <RefreshCw className="w-4 h-4" />,
        onClick: () => {
          addNotification({
            title: 'Desktop Refreshed',
            message: 'Desktop has been refreshed successfully.',
            type: 'success'
          })
        }
      },
      {
        id: 'settings',
        label: 'System Settings',
        icon: <Settings className="w-4 h-4" />,
        onClick: () => {
          const settingsIcon = desktopIcons.find(icon => icon.component === 'Settings')
          if (settingsIcon) handleIconClick(settingsIcon)
        }
      }
    ])
  }

  // Boot sequence
  if (!bootComplete) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="h-screen bg-black flex items-center justify-center"
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 2 }}
          onAnimationComplete={() => setTimeout(() => setBootComplete(true), 1000)}
          className="text-center"
        >
          <motion.div
            className="text-6xl mb-8 text-matrix-primary font-mono"
            animate={{ textShadow: ['0 0 20px #00ff41', '0 0 40px #00ff41', '0 0 20px #00ff41'] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            MATRIX OS
          </motion.div>
          
          <motion.div
            className="text-matrix-light font-mono text-sm mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
          >
            Initializing Neo Protocol...
          </motion.div>
          
          <motion.div
            className="w-64 h-2 bg-matrix-surface rounded-full overflow-hidden mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
          >
            <motion.div
              className="h-full bg-gradient-to-r from-matrix-primary to-matrix-accent"
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 2, delay: 1.5 }}
            />
          </motion.div>
          
          <motion.div
            className="text-matrix-accent font-mono text-xs mt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
          >
            "There is no spoon."
          </motion.div>
        </motion.div>
      </motion.div>
    )
  }

  return (
    <div 
      className="h-screen overflow-hidden relative"
      onContextMenu={handleDesktopContextMenu}
    >
      {/* Matrix Rain Background */}
      <MatrixRain opacity={0.4} speed={40} />
      
      {/* Desktop Icons */}
      <DesktopIcons icons={desktopIcons} onIconClick={handleIconClick} />

      {/* Windows */}
      {windowManager.getVisibleWindows().map((window) => {
        const Component = componentMap[window.component as keyof typeof componentMap]
        
        if (!Component) {
          return null
        }

        return (
          <Window
            key={window.id}
            window={window}
            isActive={window.id === windowManager.activeWindowId}
            onClose={() => windowManager.closeWindow(window.id)}
            onMinimize={() => windowManager.minimizeWindow(window.id)}
            onMaximize={() => windowManager.maximizeWindow(window.id)}
            onFocus={() => windowManager.focusWindow(window.id)}
            onPositionChange={(position) => windowManager.updateWindowPosition(window.id, position)}
            onSizeChange={(size) => windowManager.updateWindowSize(window.id, size)}
            onSnap={(zone) => windowManager.snapWindow(window.id, zone)}
          >
            <Suspense fallback={
              <div className="h-full flex items-center justify-center">
                <div className="text-matrix-primary animate-pulse">Loading...</div>
              </div>
            }>
              <Component windowId={window.id} />
            </Suspense>
          </Window>
        )
      })}

      {/* Taskbar */}
      <Taskbar
        windows={windowManager.windows}
        activeWindowId={windowManager.activeWindowId}
        onWindowRestore={windowManager.restoreWindow}
        onWindowFocus={windowManager.focusWindow}
      />
    </div>
  )
}

export default function Desktop() {
  return (
    <NotificationProvider>
      <ContextMenuProvider>
        <DesktopContent />
      </ContextMenuProvider>
    </NotificationProvider>
  )
}