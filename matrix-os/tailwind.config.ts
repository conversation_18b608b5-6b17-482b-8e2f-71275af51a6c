import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        matrix: {
          // Core brand colors with sophisticated palette
          brand: {
            50: '#f0fdf4',
            100: '#dcfce7',
            200: '#bbf7d0',
            300: '#86efac',
            400: '#4ade80',
            500: '#00ff41',
            600: '#00e639',
            700: '#00cc33',
            800: '#008f11',
            900: '#14532d',
            950: '#052e16',
          },
          // Legacy colors for backward compatibility
          primary: '#00ff41',
          secondary: '#008f11',
          accent: '#40ff8a',
          light: '#e8ffe8',
          dark: '#0a0a0a',
          medium: '#1a1a1a',
          surface: '#0f0f0f',
          // Enhanced semantic colors
          success: {
            light: '#40ff8a',
            DEFAULT: '#00ff41',
            dark: '#008f11',
            50: 'rgba(0, 255, 65, 0.05)',
            100: 'rgba(0, 255, 65, 0.1)',
            200: 'rgba(0, 255, 65, 0.2)',
          },
          warning: {
            light: '#ffeb3b',
            DEFAULT: '#ff9800',
            dark: '#e65100',
          },
          error: {
            light: '#ef5350',
            DEFAULT: '#f44336',
            dark: '#c62828',
          },
          info: {
            light: '#64b5f6',
            DEFAULT: '#2196f3',
            dark: '#1976d2',
          },
          // Enhanced neutrals with alpha support
          neutral: {
            0: 'rgba(0, 0, 0, 0)',
            50: 'rgba(255, 255, 255, 0.02)',
            100: 'rgba(255, 255, 255, 0.05)',
            200: 'rgba(255, 255, 255, 0.1)',
            300: 'rgba(255, 255, 255, 0.15)',
            400: 'rgba(255, 255, 255, 0.2)',
            500: 'rgba(255, 255, 255, 0.3)',
            600: 'rgba(255, 255, 255, 0.4)',
            700: 'rgba(0, 0, 0, 0.6)',
            800: 'rgba(0, 0, 0, 0.8)',
            900: 'rgba(0, 0, 0, 0.95)',
            950: 'rgba(0, 0, 0, 0.98)',
          },
        },
      },
      fontFamily: {
        // Enhanced font system with variable fonts
        display: ['JetBrains Mono Variable', 'SF Mono', 'Monaco', 'Share Tech Mono', 'monospace'],
        mono: ['JetBrains Mono', 'Fira Code', 'SF Mono', 'Share Tech Mono', 'monospace'],
        sans: ['Inter Variable', 'SF Pro Display', 'Roboto', 'system-ui', 'sans-serif'],
        body: ['Roboto', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        // Sophisticated typography scale with line heights and letter spacing
        'xs': ['0.75rem', { lineHeight: '1rem', letterSpacing: '0.025em' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem', letterSpacing: '0.02em' }],
        'base': ['1rem', { lineHeight: '1.5rem', letterSpacing: '0.01em' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem', letterSpacing: '0.005em' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem', letterSpacing: '0' }],
        '2xl': ['1.5rem', { lineHeight: '2rem', letterSpacing: '-0.005em' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem', letterSpacing: '-0.01em' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem', letterSpacing: '-0.015em' }],
        '5xl': ['3rem', { lineHeight: '1', letterSpacing: '-0.02em' }],
        '6xl': ['3.75rem', { lineHeight: '1', letterSpacing: '-0.025em' }],
      },
      spacing: {
        // Enhanced spacing system for consistent rhythm
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
        '144': '36rem',
      },
      borderRadius: {
        // Professional border radius scale
        'none': '0',
        'sm': '0.25rem',
        'md': '0.375rem',
        'lg': '0.5rem',
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
        'full': '9999px',
      },
      boxShadow: {
        // Enterprise-grade shadow system
        'xs': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'sm': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
        'matrix-sm': '0 2px 4px rgba(0, 255, 65, 0.05)',
        'matrix-md': '0 4px 12px rgba(0, 255, 65, 0.1), 0 2px 4px rgba(0, 0, 0, 0.3)',
        'matrix-lg': '0 8px 32px rgba(0, 255, 65, 0.15), 0 4px 8px rgba(0, 0, 0, 0.4)',
        'matrix-xl': '0 16px 64px rgba(0, 255, 65, 0.2), 0 8px 16px rgba(0, 0, 0, 0.5)',
        'matrix-glow': '0 0 20px rgba(0, 255, 65, 0.3), 0 0 40px rgba(0, 255, 65, 0.1)',
        'matrix-glow-lg': '0 0 30px rgba(0, 255, 65, 0.4), 0 0 60px rgba(0, 255, 65, 0.2)',
        'glass': '0 8px 32px rgba(0, 255, 65, 0.15), 0 1px 0 rgba(255, 255, 255, 0.1) inset, 0 -1px 0 rgba(0, 0, 0, 0.1) inset',
        'elevation-1': '0 1px 3px rgba(0, 0, 0, 0.2), 0 4px 12px rgba(0, 255, 65, 0.1)',
        'elevation-2': '0 4px 12px rgba(0, 0, 0, 0.15), 0 8px 24px rgba(0, 255, 65, 0.1)',
        'elevation-3': '0 8px 24px rgba(0, 0, 0, 0.15), 0 16px 32px rgba(0, 255, 65, 0.1)',
        'elevation-4': '0 16px 32px rgba(0, 0, 0, 0.15), 0 24px 48px rgba(0, 255, 65, 0.1)',
      },
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '12px',
        'lg': '16px',
        'xl': '24px',
        '2xl': '32px',
        '3xl': '64px',
      },
      animation: {
        // Enhanced animation system
        'matrix-rain': 'matrix-rain 20s linear infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'glow-pulse': 'glow-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'terminal-blink': 'terminal-blink 1s step-end infinite',
        'fade-in': 'fade-in 0.5s ease-out',
        'fade-out': 'fade-out 0.3s ease-in',
        'slide-up': 'slide-up 0.4s cubic-bezier(0.16, 1, 0.3, 1)',
        'slide-down': 'slide-down 0.4s cubic-bezier(0.16, 1, 0.3, 1)',
        'scale-in': 'scale-in 0.3s cubic-bezier(0.16, 1, 0.3, 1)',
        'shimmer': 'shimmer 2s linear infinite',
        'float': 'float 6s ease-in-out infinite',
        'bounce-subtle': 'bounce-subtle 2s infinite',
        'spin-slow': 'spin 3s linear infinite',
        'ping-slow': 'ping 3s cubic-bezier(0, 0, 0.2, 1) infinite',
      },
      keyframes: {
        'matrix-rain': {
          '0%': { transform: 'translateY(-100vh)' },
          '100%': { transform: 'translateY(100vh)' },
        },
        'glow': {
          '0%': { 
            textShadow: '0 0 20px rgba(0, 255, 65, 0.3), 0 0 40px rgba(0, 255, 65, 0.2)',
            boxShadow: '0 0 20px rgba(0, 255, 65, 0.3)'
          },
          '100%': { 
            textShadow: '0 0 30px rgba(0, 255, 65, 0.5), 0 0 60px rgba(0, 255, 65, 0.3)',
            boxShadow: '0 0 30px rgba(0, 255, 65, 0.5)'
          },
        },
        'glow-pulse': {
          '0%, 100%': { 
            opacity: '1',
            boxShadow: '0 0 20px rgba(0, 255, 65, 0.4)'
          },
          '50%': { 
            opacity: '0.8',
            boxShadow: '0 0 30px rgba(0, 255, 65, 0.6), 0 0 40px rgba(0, 255, 65, 0.2)'
          },
        },
        'terminal-blink': {
          'from, to': { borderColor: 'transparent' },
          '50%': { borderColor: 'rgba(0, 255, 65, 1)' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'fade-out': {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        'slide-up': {
          '0%': { 
            opacity: '0',
            transform: 'translateY(20px)'
          },
          '100%': { 
            opacity: '1',
            transform: 'translateY(0)'
          },
        },
        'slide-down': {
          '0%': { 
            opacity: '0',
            transform: 'translateY(-20px)'
          },
          '100%': { 
            opacity: '1',
            transform: 'translateY(0)'
          },
        },
        'scale-in': {
          '0%': { 
            opacity: '0',
            transform: 'scale(0.95)'
          },
          '100%': { 
            opacity: '1',
            transform: 'scale(1)'
          },
        },
        'shimmer': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'bounce-subtle': {
          '0%, 100%': {
            transform: 'translateY(-5%)',
            animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'none',
            animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
      },
      transitionTimingFunction: {
        'bounce-in': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
        'smooth-in': 'cubic-bezier(0.4, 0, 1, 1)',
        'smooth-out': 'cubic-bezier(0, 0, 0.2, 1)',
        'spring': 'cubic-bezier(0.16, 1, 0.3, 1)',
      },
      transitionDuration: {
        '400': '400ms',
        '600': '600ms',
        '800': '800ms',
        '900': '900ms',
        '1200': '1200ms',
      },
    },
  },
  plugins: [],
}

export default config