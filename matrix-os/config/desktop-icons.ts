import { DesktopIcon } from '@/types'

export const desktopIcons: DesktopIcon[] = [
  {
    id: 'terminal',
    name: 'Terminal',
    icon: 'terminal',
    position: { x: 40, y: 60 },
    component: 'Terminal',
    windowProps: {
      size: { width: 900, height: 600 },
      isResizable: true,
    }
  },
  {
    id: 'about',
    name: 'About Me',
    icon: 'user',
    position: { x: 40, y: 170 },
    component: 'About',
    windowProps: {
      size: { width: 1000, height: 700 },
      isResizable: true,
    }
  },
  {
    id: 'projects',
    name: 'Projects',
    icon: 'folder',
    position: { x: 40, y: 280 },
    component: 'FileExplorer',
    windowProps: {
      size: { width: 800, height: 600 },
      isResizable: true,
    }
  },
  {
    id: 'chat',
    name: 'AI Chat',
    icon: 'chat',
    position: { x: 40, y: 390 },
    component: 'Chat',
    windowProps: {
      size: { width: 700, height: 500 },
      isResizable: true,
    }
  },
  {
    id: 'portfolio',
    name: 'Portfolio Site',
    icon: 'globe',
    position: { x: 40, y: 500 },
    component: 'Portfolio',
    windowProps: {
      size: { width: 800, height: 600 },
      isResizable: true,
    }
  },
  {
    id: 'settings',
    name: 'System Settings',
    icon: 'settings',
    position: { x: 40, y: 610 },
    component: 'Settings',
    windowProps: {
      size: { width: 900, height: 650 },
      isResizable: true,
    }
  },
]