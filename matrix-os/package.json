{"name": "matrix-os", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.0", "react-draggable": "^4.4.6", "lucide-react": "^0.292.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.0.0", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0"}}