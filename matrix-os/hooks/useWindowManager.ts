'use client'

import { useState, useCallback, useMemo, useEffect } from 'react'
import { WindowState } from '@/types'
import { generateId, debounce } from '@/lib/utils'

interface SnapZone {
  x: number
  y: number
  width: number
  height: number
  type: 'left' | 'right' | 'top' | 'bottom' | 'center'
}

export function useWindowManager() {
  const [windows, setWindows] = useState<WindowState[]>([])
  const [activeWindowId, setActiveWindowId] = useState<string | null>(null)
  const [nextZIndex, setNextZIndex] = useState(1000)
  const [snapZones, setSnapZones] = useState<SnapZone[]>([])

  // Memoized visible windows for performance
  const visibleWindows = useMemo(() => 
    windows.filter(w => w.isOpen && !w.isMinimized),
    [windows]
  )

  // Initialize snap zones
  useEffect(() => {
    const updateSnapZones = () => {
      const zones: SnapZone[] = [
        { x: 0, y: 0, width: window.innerWidth / 2, height: window.innerHeight - 48, type: 'left' },
        { x: window.innerWidth / 2, y: 0, width: window.innerWidth / 2, height: window.innerHeight - 48, type: 'right' },
        { x: 0, y: 0, width: window.innerWidth, height: window.innerHeight - 48, type: 'center' },
      ]
      setSnapZones(zones)
    }

    updateSnapZones()
    window.addEventListener('resize', updateSnapZones)
    return () => window.removeEventListener('resize', updateSnapZones)
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey && e.key === 'Tab') {
        e.preventDefault()
        cycleWindows()
      }
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case 'w':
            e.preventDefault()
            if (activeWindowId) closeWindow(activeWindowId)
            break
          case 'm':
            e.preventDefault()
            if (activeWindowId) minimizeWindow(activeWindowId)
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [activeWindowId])

  const cycleWindows = useCallback(() => {
    if (visibleWindows.length === 0) return
    
    const currentIndex = visibleWindows.findIndex(w => w.id === activeWindowId)
    const nextIndex = (currentIndex + 1) % visibleWindows.length
    const nextWindow = visibleWindows[nextIndex]
    
    if (nextWindow) {
      focusWindow(nextWindow.id)
    }
  }, [visibleWindows, activeWindowId])

  const createWindow = useCallback((
    title: string,
    component: string,
    options: Partial<WindowState> = {}
  ): string => {
    const id = generateId()
    const newWindow: WindowState = {
      id,
      title,
      component,
      isOpen: true,
      isMinimized: false,
      isMaximized: false,
      position: { x: 100 + windows.length * 30, y: 100 + windows.length * 30 },
      size: { width: 800, height: 600 },
      zIndex: nextZIndex,
      isResizable: true,
      isDraggable: true,
      ...options,
    }

    setWindows(prev => [...prev, newWindow])
    setActiveWindowId(id)
    setNextZIndex(prev => prev + 1)
    return id
  }, [windows.length, nextZIndex])

  const closeWindow = useCallback((id: string) => {
    setWindows(prev => prev.filter(window => window.id !== id))
    setActiveWindowId(prev => prev === id ? null : prev)
  }, [])

  const minimizeWindow = useCallback((id: string) => {
    setWindows(prev =>
      prev.map(window =>
        window.id === id ? { ...window, isMinimized: true } : window
      )
    )
  }, [])

  const maximizeWindow = useCallback((id: string) => {
    setWindows(prev =>
      prev.map(window =>
        window.id === id
          ? { ...window, isMaximized: !window.isMaximized, isMinimized: false }
          : window
      )
    )
  }, [])

  const restoreWindow = useCallback((id: string) => {
    setWindows(prev =>
      prev.map(window =>
        window.id === id
          ? { ...window, isMinimized: false, isMaximized: false }
          : window
      )
    )
    setActiveWindowId(id)
  }, [])

  const focusWindow = useCallback((id: string) => {
    const maxZ = Math.max(...windows.map(w => w.zIndex), nextZIndex)
    setWindows(prev =>
      prev.map(window =>
        window.id === id ? { ...window, zIndex: maxZ + 1 } : window
      )
    )
    setActiveWindowId(id)
    setNextZIndex(maxZ + 2)
  }, [windows, nextZIndex])

  const snapWindow = useCallback((windowId: string, zone: SnapZone) => {
    setWindows(prev => prev.map(window => {
      if (window.id === windowId) {
        return {
          ...window,
          position: { x: zone.x, y: zone.y },
          size: { width: zone.width, height: zone.height },
          isMaximized: zone.type === 'center'
        }
      }
      return window
    }))
  }, [])

  const getSnapZone = useCallback((x: number, y: number): SnapZone | null => {
    const threshold = 50
    
    if (x < threshold) {
      return snapZones.find(z => z.type === 'left') || null
    }
    if (x > window.innerWidth - threshold) {
      return snapZones.find(z => z.type === 'right') || null
    }
    if (y < threshold) {
      return snapZones.find(z => z.type === 'center') || null
    }
    
    return null
  }, [snapZones])

  const updateWindowPosition = useCallback(
    debounce((id: string, position: { x: number; y: number }) => {
      setWindows(prev => prev.map(window => 
        window.id === id ? { ...window, position } : window
      ))
    }, 16), // 60fps
    []
  )

  const updateWindowSize = useCallback(
    debounce((id: string, size: { width: number; height: number }) => {
      setWindows(prev => prev.map(window => 
        window.id === id ? { ...window, size } : window
      ))
    }, 16), // 60fps
    []
  )

  const getWindow = useCallback((id: string): WindowState | undefined => {
    return windows.find(window => window.id === id)
  }, [windows])

  const getVisibleWindows = useCallback(() => {
    return visibleWindows.sort((a, b) => a.zIndex - b.zIndex)
  }, [visibleWindows])

  const getMinimizedWindows = useCallback((): WindowState[] => {
    return windows.filter(window => window.isMinimized)
  }, [windows])

  return {
    windows,
    activeWindowId,
    visibleWindows,
    snapZones,
    createWindow,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    restoreWindow,
    focusWindow,
    updateWindowPosition,
    updateWindowSize,
    getWindow,
    getVisibleWindows,
    getMinimizedWindows,
    snapWindow,
    getSnapZone,
    cycleWindows,
  }
}