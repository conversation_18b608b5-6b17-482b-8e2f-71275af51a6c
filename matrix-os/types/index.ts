export interface WindowState {
  id: string;
  title: string;
  component: string;
  isOpen: boolean;
  isMinimized: boolean;
  isMaximized: boolean;
  position: { x: number; y: number };
  size: { width: number; height: number };
  zIndex: number;
  isResizable: boolean;
  isDraggable: boolean;
  icon?: string;
}

export interface DesktopIcon {
  id: string;
  name: string;
  icon: string;
  position: { x: number; y: number };
  component: string;
  windowProps?: Partial<WindowState>;
}

export interface SystemNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: Date;
  autoHide?: boolean;
  duration?: number;
}

export interface UserProfile {
  name: string;
  title: string;
  email: string;
  phone: string;
  website: string;
  linkedin: string;
  github: string;
  avatar?: string;
}

export interface TerminalSession {
  id: string;
  history: Array<{
    input: string;
    output: string;
    timestamp: Date;
  }>;
  currentPath: string;
  isActive: boolean;
}

export interface FileSystemItem {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size?: number;
  lastModified: Date;
  parent?: string;
  children?: string[];
  content?: string;
  icon?: string;
}