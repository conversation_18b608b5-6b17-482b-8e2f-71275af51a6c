[debug] [2025-07-01T08:41:06.929Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-01T08:41:06.932Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-04T03:09:33.026Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-04T03:09:33.028Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-04T03:42:45.907Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-04T03:42:45.910Z] > authorizing via signed-in user (<EMAIL>)
