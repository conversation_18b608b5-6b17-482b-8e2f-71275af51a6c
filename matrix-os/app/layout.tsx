import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Matrix OS - Aubrey <PERSON>',
  description: 'Enter the Matrix - A digital operating system experience showcasing <PERSON>'s portfolio and expertise in cloud technology, AI, and network security.',
  keywords: ['Matrix OS', '<PERSON>', 'Cloud Consultant', 'AI Solutions', 'Portfolio', 'Operating System'],
  authors: [{ name: '<PERSON>', url: 'https://aubreyzemba.com' }],
  creator: '<PERSON>',
  publisher: 'AZLabs',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: 'Matrix OS - Aubrey Zemba',
    description: 'Enter the Matrix - A digital operating system experience',
    url: 'https://aubreyzemba.com',
    siteName: 'Matrix OS',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Matrix OS - Aubrey Zemba',
    description: 'Enter the Matrix - A digital operating system experience',
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  themeColor: '#00ff41',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full">
      <body className="h-full font-sans antialiased">
        {children}
      </body>
    </html>
  )
}