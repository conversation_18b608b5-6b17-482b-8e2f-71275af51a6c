@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&family=Inter:wght@100..900&family=Share+Tech+Mono&family=Roboto:wght@400;500;700&display=swap');

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    font-feature-settings: 'cv01', 'cv03', 'cv04', 'cv09';
  }

  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f2f0f 100%);
    color: #00ff41;
    overflow: hidden;
    position: relative;
    height: 100vh;
    width: 100vw;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
  }

  /* Enhanced focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid theme('colors.matrix.primary');
    outline-offset: 2px;
    border-radius: theme('borderRadius.sm');
  }

  /* Improved selection styles */
  ::selection {
    background-color: rgba(0, 255, 65, 0.2);
    color: theme('colors.matrix.light');
  }

  ::-moz-selection {
    background-color: rgba(0, 255, 65, 0.2);
    color: theme('colors.matrix.light');
  }
}

@layer components {
  /* Enhanced Matrix text utilities */
  .matrix-text {
    @apply text-matrix-primary font-mono;
  }

  .matrix-text-glow {
    @apply text-matrix-primary font-mono;
    text-shadow: 0 0 10px rgba(0, 255, 65, 0.5), 0 0 20px rgba(0, 255, 65, 0.3);
  }

  .matrix-text-subtle {
    @apply text-matrix-light/80 font-sans;
  }

  /* Enhanced glow effects */
  .matrix-glow {
    @apply shadow-matrix-glow;
  }

  .matrix-glow-lg {
    @apply shadow-matrix-glow-lg;
  }

  .matrix-glow-pulse {
    @apply animate-glow-pulse;
  }

  /* Professional border styles */
  .matrix-border {
    @apply border border-matrix-primary/30;
  }

  .matrix-border-glow {
    @apply border border-matrix-primary/50;
    box-shadow: 0 0 0 1px rgba(0, 255, 65, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Enterprise window styling */
  .window-shadow {
    @apply shadow-glass;
  }

  .os-window {
    @apply bg-gradient-to-br from-matrix-surface/95 to-black/90 backdrop-blur-2xl matrix-border-glow rounded-xl window-shadow;
    border-image: linear-gradient(135deg, rgba(0, 255, 65, 0.3), rgba(0, 255, 65, 0.1)) 1;
  }

  .os-window-header {
    @apply bg-gradient-to-r from-matrix-neutral-100 to-matrix-neutral-50 backdrop-blur-xl border-b border-matrix-primary/20 rounded-t-xl;
    background: linear-gradient(135deg, rgba(0, 255, 65, 0.05) 0%, rgba(0, 255, 65, 0.02) 100%);
  }

  .os-window-content {
    @apply bg-gradient-to-br from-matrix-neutral-50 to-transparent;
  }

  /* Enhanced taskbar */
  .taskbar {
    @apply bg-gradient-to-r from-matrix-surface/95 to-matrix-medium/85 backdrop-blur-2xl border-t border-matrix-primary/30;
    box-shadow: 
      0 -4px 24px rgba(0, 255, 65, 0.1),
      0 -1px 0 rgba(0, 255, 65, 0.2) inset,
      0 1px 0 rgba(0, 0, 0, 0.1) inset;
  }

  .taskbar-item {
    @apply relative px-3 py-2 rounded-lg transition-all duration-300 ease-spring;
    @apply hover:bg-matrix-primary/15 hover:shadow-matrix-md hover:scale-105;
    @apply focus-visible:bg-matrix-primary/20 focus-visible:shadow-matrix-glow;
    @apply active:scale-95;
  }

  .taskbar-item.active {
    @apply bg-matrix-primary/20 shadow-matrix-md;
  }

  .taskbar-item.active::before {
    content: '';
    @apply absolute -top-0.5 left-1/2 w-8 h-0.5 bg-matrix-primary rounded-full;
    transform: translateX(-50%);
    box-shadow: 0 0 8px rgba(0, 255, 65, 0.6);
  }

  /* Professional desktop icons */
  .desktop-icon {
    @apply flex flex-col items-center justify-center p-4 rounded-xl transition-all duration-400 ease-spring;
    @apply hover:bg-matrix-primary/10 hover:shadow-matrix-md hover:scale-105;
    @apply focus-visible:bg-matrix-primary/15 focus-visible:shadow-matrix-glow;
    @apply active:scale-95 cursor-pointer;
    @apply border border-transparent hover:border-matrix-primary/20;
  }

  .desktop-icon.selected {
    @apply bg-matrix-primary/15 border-matrix-primary/40 shadow-matrix-md;
  }

  .desktop-icon-image {
    @apply w-12 h-12 mb-2 transition-all duration-300 ease-spring;
    @apply filter drop-shadow-lg;
  }

  .desktop-icon:hover .desktop-icon-image {
    @apply filter drop-shadow-2xl;
    filter: drop-shadow(0 0 12px rgba(0, 255, 65, 0.4));
  }

  .desktop-icon-label {
    @apply text-sm font-medium text-matrix-light text-center leading-tight;
    @apply transition-colors duration-300;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  }

  .desktop-icon:hover .desktop-icon-label {
    @apply text-matrix-primary;
    text-shadow: 0 0 8px rgba(0, 255, 65, 0.4);
  }

  /* Professional button styles */
  .btn-matrix {
    @apply px-4 py-2 bg-gradient-to-r from-matrix-primary/20 to-matrix-primary/10;
    @apply border border-matrix-primary/30 rounded-lg font-medium;
    @apply transition-all duration-300 ease-spring;
    @apply hover:from-matrix-primary/30 hover:to-matrix-primary/20 hover:border-matrix-primary/50;
    @apply hover:shadow-matrix-md hover:scale-105;
    @apply focus-visible:shadow-matrix-glow focus-visible:scale-105;
    @apply active:scale-95;
  }

  .btn-matrix-primary {
    @apply bg-gradient-to-r from-matrix-primary to-matrix-primary/80;
    @apply text-matrix-dark font-semibold border-matrix-primary;
    @apply hover:from-matrix-primary hover:to-matrix-primary;
    @apply hover:shadow-matrix-glow-lg;
  }

  .btn-matrix-outline {
    @apply bg-transparent border-2 border-matrix-primary/50 text-matrix-primary;
    @apply hover:bg-matrix-primary/10 hover:border-matrix-primary;
    @apply hover:shadow-matrix-glow;
  }

  /* Enhanced input styles */
  .input-matrix {
    @apply w-full px-4 py-3 bg-matrix-surface/50 border border-matrix-primary/30 rounded-lg;
    @apply text-matrix-light placeholder-matrix-light/50 font-mono;
    @apply transition-all duration-300 ease-spring backdrop-blur-md;
    @apply focus:border-matrix-primary focus:shadow-matrix-glow focus:bg-matrix-surface/70;
    @apply hover:border-matrix-primary/50 hover:bg-matrix-surface/60;
  }

  /* Terminal-specific styles */
  .terminal-window {
    @apply os-window font-mono text-sm;
  }

  .terminal-header {
    @apply os-window-header flex items-center justify-between px-4 py-2;
  }

  .terminal-content {
    @apply p-4 h-full overflow-hidden bg-gradient-to-br from-black/95 to-matrix-dark/95;
  }

  .terminal-line {
    @apply flex items-center min-h-[1.5rem] text-matrix-light;
  }

  .terminal-prompt {
    @apply text-matrix-primary mr-2 select-none;
  }

  .terminal-cursor {
    @apply w-2 h-5 bg-matrix-primary animate-terminal-blink ml-1;
  }

  /* Chat window styles */
  .chat-window {
    @apply os-window;
  }

  .chat-header {
    @apply os-window-header flex items-center justify-between px-4 py-3;
  }

  .chat-content {
    @apply flex flex-col h-full;
  }

  .chat-messages {
    @apply flex-1 overflow-y-auto scrollbar-thin p-4 space-y-3;
  }

  .chat-message {
    @apply p-3 rounded-lg max-w-[80%] animate-slide-up;
  }

  .chat-message.user {
    @apply bg-matrix-primary/20 border border-matrix-primary/30 ml-auto text-right;
  }

  .chat-message.assistant {
    @apply bg-matrix-surface/50 border border-matrix-primary/20 mr-auto;
  }

  .chat-input-area {
    @apply border-t border-matrix-primary/20 p-4 bg-matrix-surface/30 backdrop-blur-md;
  }

  /* Loading and state indicators */
  .loading-shimmer {
    @apply relative overflow-hidden;
  }

  .loading-shimmer::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-matrix-primary/10 to-transparent;
    @apply animate-shimmer;
  }

  .status-indicator {
    @apply w-2 h-2 rounded-full;
  }

  .status-indicator.online {
    @apply bg-matrix-success shadow-[0_0_6px_rgba(0,255,65,0.5)] animate-glow-pulse;
  }

  .status-indicator.offline {
    @apply bg-matrix-neutral-400;
  }

  .status-indicator.away {
    @apply bg-matrix-warning shadow-[0_0_6px_rgba(255,152,0,0.5)];
  }

  /* Enhanced animations */
  .animate-float {
    @apply animate-float;
  }

  .animate-bounce-subtle {
    @apply animate-bounce-subtle;
  }

  /* Responsive text scaling */
  .text-responsive {
    @apply text-sm md:text-base lg:text-lg;
  }

  .text-responsive-lg {
    @apply text-base md:text-lg lg:text-xl;
  }

  /* Accessibility enhancements */
  .screen-reader-only {
    @apply sr-only;
  }

  .focus-ring {
    @apply focus-visible:ring-2 focus-visible:ring-matrix-primary focus-visible:ring-offset-2 focus-visible:ring-offset-matrix-dark;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-matrix-rain,
    .animate-glow,
    .animate-glow-pulse,
    .animate-float,
    .animate-bounce-subtle,
    .animate-shimmer {
      @apply animate-none;
    }

    .transition-all,
    .transition-colors,
    .transition-transform {
      @apply transition-none;
    }
  }

  /* High contrast support */
  @media (prefers-contrast: high) {
    .matrix-border {
      @apply border-matrix-primary;
    }

    .matrix-text-subtle {
      @apply text-matrix-light;
    }

    .btn-matrix {
      @apply border-2 border-matrix-primary;
    }
  }
}

@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #00ff41 transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #00ff41;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #40ff8a;
  }

  /* Range slider styles */
  .slider {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }

  .slider::-webkit-slider-track {
    background: rgba(15, 15, 15, 0.8);
    height: 8px;
    border-radius: 4px;
    border: 1px solid rgba(0, 255, 65, 0.3);
  }

  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00ff41, #40ff8a);
    cursor: pointer;
    border: 2px solid #0a0a0a;
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
  }

  .slider::-webkit-slider-thumb:hover {
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.8);
    transform: scale(1.1);
  }

  .slider::-moz-range-track {
    background: rgba(15, 15, 15, 0.8);
    height: 8px;
    border-radius: 4px;
    border: 1px solid rgba(0, 255, 65, 0.3);
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00ff41, #40ff8a);
    cursor: pointer;
    border: 2px solid #0a0a0a;
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
  }
}